# 🧪 LOCAL TESTING GUIDE

## 🚀 STEP 1: SETUP LOCAL ENVIRONMENT

### 1.1 Install Dependencies
```bash
cd school-website
npm install
```

### 1.2 Create Environment File
Create `.env.local` file with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...your-anon-key
SUPABASE_SERVICE_ROLE_KEY=eyJ...your-service-role-key
JWT_SECRET=your-jwt-secret-key-here
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## 🗄️ STEP 2: SETUP DATABASE

### Option A: Manual Setup (Recommended)
1. Go to **Supabase Dashboard** → **SQL Editor**
2. Copy and paste the SQL from `setup-db.js` file
3. Run the SQL to create all tables

### Option B: Try Automated Setup
```bash
node setup-db.js
```

## 🔍 STEP 3: TEST AUTHENTICATION

### 3.1 Run Authentication Test
```bash
node test-auth.js
```

This will:
- ✅ Check if database tables exist
- ✅ Check for existing admin users
- ✅ Create a test user if none exists
- ✅ Test password verification
- ✅ Provide login credentials

### 3.2 Expected Output
```
🔍 Testing Supabase Connection...

1. Checking admin_users table...
✅ admin_users table exists

2. Checking for existing admin users...
📊 Found 1 admin users:
   - <EMAIL> (School Administrator) - Role: super_admin - Active: true

3. Testing authentication...
✅ User found: <EMAIL>
🔐 Password verification: ✅ Valid

🎉 Authentication test PASSED!
📝 Use these credentials to login:
   Email: <EMAIL>
   Password: admin123
```

## 🌐 STEP 4: START LOCAL SERVER

```bash
npm run dev
```

Visit: http://localhost:3000

## 🔐 STEP 5: TEST ADMIN LOGIN

1. **Go to**: http://localhost:3000/admin/login
2. **Login with**:
   - Email: `<EMAIL>`
   - Password: `admin123`

## 🐛 TROUBLESHOOTING

### Issue: "admin_users table error"
**Solution**: Run the database setup SQL manually in Supabase

### Issue: "Invalid email or password"
**Solutions**:
1. Check if user exists: `node test-auth.js`
2. Verify environment variables in `.env.local`
3. Check Supabase connection

### Issue: "Failed to create session"
**Solutions**:
1. Check if `admin_sessions` table exists
2. Verify database permissions
3. Check server logs for detailed errors

### Issue: Network errors
**Solutions**:
1. Verify Supabase URL and keys
2. Check internet connection
3. Verify Supabase project is active

## 📋 MANUAL DATABASE SETUP SQL

If automated setup fails, run this SQL in Supabase SQL Editor:

```sql
-- Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(50) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin_sessions table
CREATE TABLE IF NOT EXISTS admin_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  admin_user_id UUID REFERENCES admin_users(id) ON DELETE CASCADE,
  session_token TEXT UNIQUE NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create other tables (announcements, faculty, achievements, gallery, contact_submissions, school_settings, audit_logs)
-- [Copy the rest from setup-db.js]

-- Create test admin user
INSERT INTO admin_users (email, password_hash, name, role, is_active)
VALUES (
  '<EMAIL>',
  '$2a$12$LQv3c1yqBwEHXw.9oC9.Oe.9fxvdHDcyLXvI1KJn.2rjVxrjVxrjV',
  'School Administrator',
  'super_admin',
  true
);
```

## ✅ SUCCESS CRITERIA

When everything works correctly:
- ✅ `node test-auth.js` shows "Authentication test PASSED!"
- ✅ Local server starts without errors
- ✅ Admin login works at http://localhost:3000/admin/login
- ✅ Admin dashboard loads successfully
- ✅ No console errors in browser

## 🚀 NEXT STEPS

Once local testing passes:
1. **Commit the fixes** to your repository
2. **Deploy to Render** (it should work now)
3. **Set up production database** with the same SQL
4. **Test production login**

## 📞 SUPPORT

If you encounter issues:
1. **Check the console logs** for detailed error messages
2. **Verify environment variables** are correct
3. **Test database connection** in Supabase dashboard
4. **Run the test scripts** to identify specific issues
