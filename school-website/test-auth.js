// Test authentication locally
const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testAuth() {
  console.log('🔍 Testing Supabase Connection...');
  
  try {
    // Test 1: Check if admin_users table exists
    console.log('\n1. Checking admin_users table...');
    const { data: tables, error: tableError } = await supabase
      .from('admin_users')
      .select('*')
      .limit(1);
    
    if (tableError) {
      console.error('❌ admin_users table error:', tableError.message);
      console.log('💡 You need to create the database tables first!');
      return;
    }
    
    console.log('✅ admin_users table exists');
    
    // Test 2: Check if any admin users exist
    console.log('\n2. Checking for existing admin users...');
    const { data: users, error: usersError } = await supabase
      .from('admin_users')
      .select('id, email, name, role, is_active');
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError.message);
      return;
    }
    
    console.log(`📊 Found ${users.length} admin users:`);
    users.forEach(user => {
      console.log(`   - ${user.email} (${user.name}) - Role: ${user.role} - Active: ${user.is_active}`);
    });
    
    if (users.length === 0) {
      console.log('\n🔧 Creating test admin user...');
      await createTestUser();
    }
    
    // Test 3: Test authentication
    console.log('\n3. Testing authentication...');
    const testEmail = '<EMAIL>';
    const testPassword = 'admin123';
    
    const { data: user, error: authError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', testEmail)
      .eq('is_active', true)
      .single();
    
    if (authError || !user) {
      console.error('❌ User not found:', authError?.message);
      return;
    }
    
    console.log('✅ User found:', user.email);
    
    // Test password verification
    const isValidPassword = await bcrypt.compare(testPassword, user.password_hash);
    console.log('🔐 Password verification:', isValidPassword ? '✅ Valid' : '❌ Invalid');
    
    if (isValidPassword) {
      console.log('\n🎉 Authentication test PASSED!');
      console.log('📝 Use these credentials to login:');
      console.log(`   Email: ${testEmail}`);
      console.log(`   Password: ${testPassword}`);
    } else {
      console.log('\n❌ Authentication test FAILED!');
      console.log('🔧 Password hash might be incorrect');
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

async function createTestUser() {
  try {
    const email = '<EMAIL>';
    const password = 'admin123';
    const name = 'School Administrator';
    
    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);
    
    const { data, error } = await supabase
      .from('admin_users')
      .insert({
        email,
        password_hash: passwordHash,
        name,
        role: 'super_admin',
        is_active: true
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Error creating user:', error.message);
    } else {
      console.log('✅ Test user created successfully!');
      console.log(`   Email: ${email}`);
      console.log(`   Password: ${password}`);
    }
  } catch (error) {
    console.error('💥 Error creating test user:', error.message);
  }
}

// Run the test
testAuth();
