import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
  },
  // Ensure the app works on Render
  experimental: {
    serverComponentsExternalPackages: ['bcryptjs'],
  },
  // Output configuration for deployment
  output: 'standalone',
};

export default nextConfig;
