{"/_not-found/page": "/_not-found", "/api/admin/achievements/[id]/route": "/api/admin/achievements/[id]", "/api/admin/achievements/route": "/api/admin/achievements", "/api/admin/announcements/route": "/api/admin/announcements", "/api/admin/auth/login/route": "/api/admin/auth/login", "/api/admin/announcements/[id]/route": "/api/admin/announcements/[id]", "/api/admin/audit/route": "/api/admin/audit", "/api/admin/contacts/[id]/route": "/api/admin/contacts/[id]", "/api/admin/backup/route": "/api/admin/backup", "/api/admin/export/route": "/api/admin/export", "/api/admin/auth/logout/route": "/api/admin/auth/logout", "/api/admin/faculty/[id]/route": "/api/admin/faculty/[id]", "/api/admin/faculty/route": "/api/admin/faculty", "/api/admin/gallery/route": "/api/admin/gallery", "/api/admin/gallery/[id]/route": "/api/admin/gallery/[id]", "/api/admin/settings/route": "/api/admin/settings", "/api/admin/init-db/route": "/api/admin/init-db", "/api/admin/users/route": "/api/admin/users", "/api/admin/upload/route": "/api/admin/upload", "/api/admin/contacts/route": "/api/admin/contacts", "/api/admin/gallery/bulk/route": "/api/admin/gallery/bulk", "/api/search/route": "/api/search", "/api/admin/scheduled-publish/route": "/api/admin/scheduled-publish", "/api/contact/route": "/api/contact", "/favicon.ico/route": "/favicon.ico", "/api/settings/route": "/api/settings", "/admin/achievements/new/page": "/admin/achievements/new", "/admin/achievements/[id]/edit/page": "/admin/achievements/[id]/edit", "/admin/announcements/new/page": "/admin/announcements/new", "/admin/achievements/page": "/admin/achievements", "/admin/backup/page": "/admin/backup", "/admin/announcements/[id]/edit/page": "/admin/announcements/[id]/edit", "/admin/content/page": "/admin/content", "/admin/announcements/page": "/admin/announcements", "/admin/dashboard/page": "/admin/dashboard", "/admin/audit/page": "/admin/audit", "/admin/faculty/new/page": "/admin/faculty/new", "/admin/faculty/page": "/admin/faculty", "/admin/settings/page": "/admin/settings", "/admin/gallery/page": "/admin/gallery", "/admin/users/page": "/admin/users", "/admin/login/page": "/admin/login", "/admin/gallery/upload/page": "/admin/gallery/upload", "/admin/faculty/[id]/edit/page": "/admin/faculty/[id]/edit", "/gallery/page": "/gallery", "/page": "/", "/academics/page": "/academics", "/about/page": "/about", "/admissions/page": "/admissions", "/contact/page": "/contact", "/faculty/page": "/faculty", "/search/page": "/search"}