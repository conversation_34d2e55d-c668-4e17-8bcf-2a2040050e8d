{"/_not-found/page": "/_not-found", "/api/admin/achievements/[id]/route": "/api/admin/achievements/[id]", "/api/admin/announcements/[id]/route": "/api/admin/announcements/[id]", "/api/admin/achievements/route": "/api/admin/achievements", "/api/admin/announcements/route": "/api/admin/announcements", "/api/admin/auth/login/route": "/api/admin/auth/login", "/api/admin/audit/route": "/api/admin/audit", "/api/admin/backup/route": "/api/admin/backup", "/api/admin/auth/logout/route": "/api/admin/auth/logout", "/api/admin/contacts/[id]/route": "/api/admin/contacts/[id]", "/api/admin/faculty/route": "/api/admin/faculty", "/api/admin/faculty/[id]/route": "/api/admin/faculty/[id]", "/api/admin/export/route": "/api/admin/export", "/api/admin/contacts/route": "/api/admin/contacts", "/api/admin/gallery/bulk/route": "/api/admin/gallery/bulk", "/api/admin/gallery/route": "/api/admin/gallery", "/api/admin/gallery/[id]/route": "/api/admin/gallery/[id]", "/api/admin/scheduled-publish/route": "/api/admin/scheduled-publish", "/api/admin/init-db/route": "/api/admin/init-db", "/api/admin/settings/route": "/api/admin/settings", "/api/admin/upload/route": "/api/admin/upload", "/api/contact/route": "/api/contact", "/api/settings/route": "/api/settings", "/api/admin/users/route": "/api/admin/users", "/favicon.ico/route": "/favicon.ico", "/api/search/route": "/api/search", "/admin/achievements/page": "/admin/achievements", "/admin/achievements/[id]/edit/page": "/admin/achievements/[id]/edit", "/admin/achievements/new/page": "/admin/achievements/new", "/admin/announcements/new/page": "/admin/announcements/new", "/admin/announcements/[id]/edit/page": "/admin/announcements/[id]/edit", "/admin/audit/page": "/admin/audit", "/admin/announcements/page": "/admin/announcements", "/admin/content/page": "/admin/content", "/admin/backup/page": "/admin/backup", "/admin/dashboard/page": "/admin/dashboard", "/admin/faculty/[id]/edit/page": "/admin/faculty/[id]/edit", "/admin/gallery/page": "/admin/gallery", "/admin/faculty/new/page": "/admin/faculty/new", "/admin/login/page": "/admin/login", "/admin/settings/page": "/admin/settings", "/admin/users/page": "/admin/users", "/admin/faculty/page": "/admin/faculty", "/admin/gallery/upload/page": "/admin/gallery/upload", "/admin/setup/page": "/admin/setup", "/gallery/page": "/gallery", "/page": "/", "/about/page": "/about", "/academics/page": "/academics", "/admissions/page": "/admissions", "/contact/page": "/contact", "/faculty/page": "/faculty", "/search/page": "/search"}