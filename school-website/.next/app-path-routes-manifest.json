{"/_not-found/page": "/_not-found", "/api/admin/achievements/route": "/api/admin/achievements", "/api/admin/announcements/[id]/route": "/api/admin/announcements/[id]", "/api/admin/announcements/route": "/api/admin/announcements", "/api/admin/auth/login/route": "/api/admin/auth/login", "/api/admin/audit/route": "/api/admin/audit", "/api/admin/backup/route": "/api/admin/backup", "/api/admin/auth/logout/route": "/api/admin/auth/logout", "/api/admin/contacts/[id]/route": "/api/admin/contacts/[id]", "/api/admin/contacts/route": "/api/admin/contacts", "/api/admin/faculty/route": "/api/admin/faculty", "/api/admin/export/route": "/api/admin/export", "/api/admin/achievements/[id]/route": "/api/admin/achievements/[id]", "/api/admin/gallery/[id]/route": "/api/admin/gallery/[id]", "/api/admin/gallery/route": "/api/admin/gallery", "/api/admin/gallery/bulk/route": "/api/admin/gallery/bulk", "/api/admin/init-db/route": "/api/admin/init-db", "/api/admin/scheduled-publish/route": "/api/admin/scheduled-publish", "/api/admin/upload/route": "/api/admin/upload", "/api/admin/faculty/[id]/route": "/api/admin/faculty/[id]", "/api/admin/users/route": "/api/admin/users", "/api/search/route": "/api/search", "/api/settings/route": "/api/settings", "/api/contact/route": "/api/contact", "/favicon.ico/route": "/favicon.ico", "/api/admin/settings/route": "/api/admin/settings", "/admin/achievements/new/page": "/admin/achievements/new", "/admin/announcements/[id]/edit/page": "/admin/announcements/[id]/edit", "/admin/announcements/page": "/admin/announcements", "/admin/achievements/page": "/admin/achievements", "/admin/content/page": "/admin/content", "/admin/announcements/new/page": "/admin/announcements/new", "/admin/backup/page": "/admin/backup", "/admin/faculty/page": "/admin/faculty", "/admin/dashboard/page": "/admin/dashboard", "/admin/audit/page": "/admin/audit", "/admin/faculty/new/page": "/admin/faculty/new", "/admin/faculty/[id]/edit/page": "/admin/faculty/[id]/edit", "/admin/achievements/[id]/edit/page": "/admin/achievements/[id]/edit", "/admin/gallery/upload/page": "/admin/gallery/upload", "/admin/settings/page": "/admin/settings", "/admin/gallery/page": "/admin/gallery", "/admin/setup/page": "/admin/setup", "/admin/users/page": "/admin/users", "/page": "/", "/admin/login/page": "/admin/login", "/gallery/page": "/gallery", "/about/page": "/about", "/admissions/page": "/admissions", "/academics/page": "/academics", "/contact/page": "/contact", "/faculty/page": "/faculty", "/search/page": "/search"}