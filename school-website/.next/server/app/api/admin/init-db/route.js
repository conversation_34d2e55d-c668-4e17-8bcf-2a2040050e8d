(()=>{var a={};a.id=7175,a.ids=[7175],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},27910:a=>{"use strict";a.exports=require("stream")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},50218:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>G,patchFetch:()=>F,routeModule:()=>B,serverHooks:()=>E,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>D});var d={};c.r(d),c.d(d,{GET:()=>A,POST:()=>z});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(56621),w=c(85663);async function x(){try{console.log("Initializing database...");let a=(0,v.LE)(),{data:b}=await a.from("admin_users").select("id").eq("email","<EMAIL>").single();if(b)return console.log("Admin user already exists"),{success:!0,message:"Database already initialized"};let c=await w.Ay.hash("admin123",12),{data:d,error:e}=await a.from("admin_users").insert({email:"<EMAIL>",password_hash:c,name:"System Administrator",role:"super_admin",is_active:!0}).select().single();if(e)return console.error("Error creating admin user:",e),{success:!1,error:e.message};console.log("Admin user created:",d.email);let f=[{title:"Admission 2024-25 Open",content:"Applications are now open for admission to Class 11th for the academic year 2024-25. Eligible students can apply online through our official website.",priority:"high",status:"published",is_published:!0,publish_date:new Date().toISOString()},{title:"Annual Sports Day",content:"Annual Sports Day will be held on March 15, 2024. All students are encouraged to participate in various sporting events.",priority:"medium",status:"published",is_published:!0,publish_date:new Date().toISOString()},{title:"Parent-Teacher Meeting",content:"Parent-Teacher meeting is scheduled for next week. Please check the notice board for detailed schedule.",priority:"medium",status:"draft",is_published:!1,publish_date:new Date().toISOString()}],{error:g}=await a.from("announcements").insert(f);g?console.error("Error creating sample announcements:",g):console.log("Sample announcements created");let{error:h}=await a.from("faculty").insert([{name:"Dr. Rajesh Kumar",designation:"Principal",department:"Administration",qualification:"Ph.D. in Education",experience:"25 years",specialization:"Educational Administration",email:"<EMAIL>",phone:"+91 9876543210",bio:"Dr. Rajesh Kumar has been serving as Principal for over 10 years, dedicated to providing quality education to meritorious students.",is_published:!0},{name:"Mrs. Priya Sharma",designation:"Head of Mathematics Department",department:"Mathematics",qualification:"M.Sc. Mathematics, B.Ed.",experience:"15 years",specialization:"Advanced Mathematics, JEE Preparation",email:"<EMAIL>",phone:"+91 9876543211",bio:"Mrs. Priya Sharma is an experienced mathematics teacher specializing in competitive exam preparation.",is_published:!0},{name:"Dr. Amit Singh",designation:"Physics Teacher",department:"Science",qualification:"Ph.D. in Physics",experience:"12 years",specialization:"Quantum Physics, NEET Preparation",email:"<EMAIL>",phone:"+91 9876543212",bio:"Dr. Amit Singh brings extensive research experience to classroom teaching.",is_published:!0}]);h?console.error("Error creating sample faculty:",h):console.log("Sample faculty created");let{error:i}=await a.from("gallery").insert([{title:"School Campus",description:"Beautiful view of our school campus",image_url:"https://images.unsplash.com/photo-1580582932707-520aed937b7b?w=800",category:"campus",status:"published",is_published:!0},{title:"Science Laboratory",description:"Well-equipped science laboratory for practical learning",image_url:"https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=800",category:"academics",status:"published",is_published:!0},{title:"Sports Day 2023",description:"Annual sports day celebration",image_url:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800",category:"sports",status:"published",is_published:!0},{title:"Cultural Event",description:"Students performing in cultural program",image_url:"https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=800",category:"events",status:"published",is_published:!0}]);i?console.error("Error creating sample gallery:",i):console.log("Sample gallery created");let{error:j}=await a.from("achievements").insert([{title:"JEE Advanced Qualification",description:"Student qualified for JEE Advanced 2023 with All India Rank under 5000",student_name:"Arjun Sharma",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-06-15",award_by:"IIT Council",position:"AIR 4500",status:"published",is_published:!0},{title:"State Level Basketball Championship",description:"Won gold medal in state level basketball championship representing the school",student_name:"Rajveer Singh",student_class:"Class 11",achievement_type:"sports",achievement_date:"2023-04-10",award_by:"Punjab Sports Council",position:"1st Place",status:"published",is_published:!0},{title:"National Science Olympiad",description:"Secured 2nd position in National Science Olympiad 2023",student_name:"Ananya Gupta",student_class:"Class 10",achievement_type:"competition",achievement_date:"2023-03-15",award_by:"Science Olympiad Foundation",position:"2nd Place",status:"published",is_published:!0},{title:"Inter-School Cultural Fest Winner",description:"First prize in classical dance competition at inter-school cultural festival",student_name:"Simran Kaur",student_class:"Class 9",achievement_type:"cultural",achievement_date:"2023-02-28",award_by:"Cultural Association",position:"1st Place",status:"published",is_published:!0}]);return j?console.error("Error creating sample achievements:",j):console.log("Sample achievements created"),{success:!0,message:"Database initialized successfully",adminUser:{email:d.email,name:d.name,role:d.role}}}catch(a){return console.error("Database initialization error:",a),{success:!1,error:a instanceof Error?a.message:"Unknown error"}}}async function y(){try{let a=(0,v.LE)(),{data:b,error:c}=await a.from("admin_users").select("count").limit(1);if(c)return{connected:!1,error:c.message};return{connected:!0,data:b}}catch(a){return{connected:!1,error:a instanceof Error?a.message:"Unknown error"}}}async function z(a){try{let a=await y();if(!a.connected)return u.NextResponse.json({success:!1,error:"Database connection failed",details:a.error},{status:500});let b=await x();if(b.success)return u.NextResponse.json({success:!0,message:b.message,adminUser:b.adminUser});return u.NextResponse.json({success:!1,error:b.error},{status:500})}catch(a){return console.error("Init DB API error:",a),u.NextResponse.json({success:!1,error:"Internal server error",details:a instanceof Error?a.message:"Unknown error"},{status:500})}}async function A(){try{let a=await y();return u.NextResponse.json({connected:a.connected,error:a.error||null,timestamp:new Date().toISOString()})}catch(a){return u.NextResponse.json({connected:!1,error:a instanceof Error?a.message:"Unknown error",timestamp:new Date().toISOString()},{status:500})}}let B=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/admin/init-db/route",pathname:"/api/admin/init-db",filename:"route",bundlePath:"app/api/admin/init-db/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/init-db/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:C,workUnitAsyncStorage:D,serverHooks:E}=B;function F(){return(0,g.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:D})}async function G(a,b,c){var d;let e="/api/admin/init-db/route";"/index"===e&&(e="/");let g=await B.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||B.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===B.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>B.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>B.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await B.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await B.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await B.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56621:(a,b,c)=>{"use strict";c.d(b,{LE:()=>e});var d=c(66437);let e=()=>(0,d.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});(0,d.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxemJ1aGRhbnRyeXJ3eXZobm50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDE1OTcsImV4cCI6MjA2ODQxNzU5N30.Eb5fqPvaSZPEVj1ATu-JO90-chl8LreyjkXdxzOd2PM"),e()},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,5663,6055],()=>b(b.s=50218));module.exports=c})();