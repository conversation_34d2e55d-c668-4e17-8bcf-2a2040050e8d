(()=>{var a={};a.id=5262,a.ids=[5262],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8774:(a,b,c)=>{"use strict";c.d(b,{QM:()=>h,Wf:()=>j,Ww:()=>i,nJ:()=>f});let d=(0,c(66437).UU)("https://bqzbuhdantryrwyvhnnt.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY),e={image:{maxSize:5242880,allowedTypes:["image/jpeg","image/png","image/webp","image/gif"],allowedExtensions:[".jpg",".jpeg",".png",".webp",".gif"]},document:{maxSize:0xa00000,allowedTypes:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],allowedExtensions:[".pdf",".doc",".docx"]},any:{maxSize:0xa00000,allowedTypes:["image/jpeg","image/png","image/webp","image/gif","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],allowedExtensions:[".jpg",".jpeg",".png",".webp",".gif",".pdf",".doc",".docx"]}};function f(a,b="any"){let c=[],d=e[b];a.size>d.maxSize&&c.push(`File size must be less than ${function(a){if(0===a)return"0 Bytes";let b=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,b)).toFixed(2))+" "+["Bytes","KB","MB","GB"][b]}(d.maxSize)}`),d.allowedTypes.includes(a.type)||c.push(`File type ${a.type} is not allowed. Allowed types: ${d.allowedTypes.join(", ")}`);let h=g(a.name);return d.allowedExtensions.includes(h)||c.push(`File extension ${h} is not allowed. Allowed extensions: ${d.allowedExtensions.join(", ")}`),c}function g(a){return a.toLowerCase().substring(a.lastIndexOf("."))}async function h(a,b,c="",e="any"){try{let h=f(a,e);if(h.length>0)return{success:!1,error:h.join(", ")};let i=function(a){let b=g(a),c=Date.now(),d=Math.random().toString(36).substring(2,8),e=a.replace(b,"").replace(/[^a-zA-Z0-9]/g,"_");return`${e}_${c}_${d}${b}`}(a.name),j=c?`${c}/${i}`:i,{error:k}=await d.storage.from(b).upload(j,a,{cacheControl:"3600",upsert:!1});if(k)return{success:!1,error:k.message};let{data:l}=d.storage.from(b).getPublicUrl(j);return{success:!0,url:l.publicUrl,path:j}}catch(a){return{success:!1,error:a instanceof Error?a.message:"Upload failed"}}}async function i(a,b){try{let{error:c}=await d.storage.from(a).remove([b]);return!c}catch(a){return console.error("Delete file error:",a),!1}}function j(a){try{let b=new URL(a).pathname.split("/");if(b.length>=6&&"storage"===b[1]&&"public"===b[4]){let a=b[5],c=b.slice(6).join("/");return{bucket:a,path:c}}return null}catch{return null}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},12909:(a,b,c)=>{"use strict";c.d(b,{Yk:()=>m,_p:()=>l,bS:()=>h,eg:()=>i,nG:()=>k});var d=c(85663),e=c(44999);let f=(0,c(56621).LE)();async function g(a,b){return d.Ay.compare(a,b)}async function h(a,b){try{let{data:c,error:d}=await f.from("admin_users").select("*").eq("email",a).eq("is_active",!0).single();if(d||!c||!await g(b,c.password_hash))return null;await f.from("admin_users").update({last_login:new Date().toISOString()}).eq("id",c.id);let{password_hash:e,...h}=c;return h}catch(a){return console.error("Authentication error:",a),null}}async function i(a){try{let b=crypto.randomUUID()+"-"+Date.now()+"-"+Math.random().toString(36),c=new Date;c.setDate(c.getDate()+7);let{error:d}=await f.from("admin_sessions").insert({admin_user_id:a,session_token:b,expires_at:c.toISOString()});if(d)return console.error("Session creation error:",d),null;return b}catch(a){return console.error("Session creation error:",a),null}}async function j(a){try{let{data:b,error:c}=await f.from("admin_sessions").select(`
        *,
        admin_users (*)
      `).eq("session_token",a).gt("expires_at",new Date().toISOString()).single();if(c||!b||!b.admin_users)return null;let{password_hash:d,...e}=Array.isArray(b.admin_users)?b.admin_users[0]:b.admin_users;return e}catch(a){return console.error("Session validation error:",a),null}}async function k(){try{let a=await (0,e.UL)(),b=a.get("admin_session")?.value;if(!b)return null;return await j(b)}catch(a){return console.error("Get current admin error:",a),null}}async function l(a){try{let{error:b}=await f.from("admin_sessions").delete().eq("session_token",a);return!b}catch(a){return console.error("Logout error:",a),!1}}async function m(a,b,c,d,e,g,h,i){try{await f.from("audit_logs").insert({admin_user_id:a,action:b,table_name:c,record_id:d,old_values:e,new_values:g,ip_address:h,user_agent:i})}catch(a){console.error("Audit log error:",a)}}},27910:a=>{"use strict";a.exports=require("stream")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56621:(a,b,c)=>{"use strict";c.d(b,{LE:()=>e});var d=c(66437);let e=()=>(0,d.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});(0,d.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxemJ1aGRhbnRyeXJ3eXZobm50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDE1OTcsImV4cCI6MjA2ODQxNzU5N30.Eb5fqPvaSZPEVj1ATu-JO90-chl8LreyjkXdxzOd2PM"),e()},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},93277:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>H,patchFetch:()=>G,routeModule:()=>C,serverHooks:()=>F,workAsyncStorage:()=>D,workUnitAsyncStorage:()=>E});var d={};c.r(d),c.d(d,{DELETE:()=>B,GET:()=>z,PATCH:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(12909),w=c(66437),x=c(8774);let y=(0,w.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function z(a,{params:b}){try{let{id:a}=await b;if(!await (0,v.nG)())return u.NextResponse.json({error:"Unauthorized"},{status:401});let{data:c,error:d}=await y.from("gallery").select("*").eq("id",a).single();if(d)return console.error("Database error:",d),u.NextResponse.json({error:"Gallery item not found"},{status:404});return u.NextResponse.json(c)}catch(a){return console.error("API error:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function A(a,{params:b}){try{let{id:c}=await b,d=await (0,v.nG)();if(!d)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{data:e}=await y.from("gallery").select("*").eq("id",c).single(),f={...await a.json(),updated_at:new Date().toISOString()},{data:g,error:h}=await y.from("gallery").update(f).eq("id",c).select().single();if(h)return console.error("Database error:",h),u.NextResponse.json({error:"Failed to update gallery item"},{status:500});let i=a.headers.get("x-forwarded-for")||a.headers.get("x-real-ip")||"unknown",j=a.headers.get("user-agent")||"unknown";return await (0,v.Yk)(d.id,"UPDATE_GALLERY_ITEM","gallery",c,e,f,i,j),u.NextResponse.json(g)}catch(a){return console.error("API error:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function B(a,{params:b}){try{let{id:c}=await b,d=await (0,v.nG)();if(!d)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{data:e}=await y.from("gallery").select("*").eq("id",c).single();if(!e)return u.NextResponse.json({error:"Gallery item not found"},{status:404});let{error:f}=await y.from("gallery").delete().eq("id",c);if(f)return console.error("Database error:",f),u.NextResponse.json({error:"Failed to delete gallery item"},{status:500});if(e.image_url){let a=(0,x.Wf)(e.image_url);a&&await (0,x.Ww)(a.bucket,a.path)}let g=a.headers.get("x-forwarded-for")||a.headers.get("x-real-ip")||"unknown",h=a.headers.get("user-agent")||"unknown";return await (0,v.Yk)(d.id,"DELETE_GALLERY_ITEM","gallery",c,e,void 0,g,h),u.NextResponse.json({success:!0})}catch(a){return console.error("API error:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}let C=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/admin/gallery/[id]/route",pathname:"/api/admin/gallery/[id]",filename:"route",bundlePath:"app/api/admin/gallery/[id]/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/gallery/[id]/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:D,workUnitAsyncStorage:E,serverHooks:F}=C;function G(){return(0,g.patchFetch)({workAsyncStorage:D,workUnitAsyncStorage:E})}async function H(a,b,c){var d;let e="/api/admin/gallery/[id]/route";"/index"===e&&(e="/");let g=await C.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||C.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===C.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>C.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>C.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await C.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await C.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await C.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,5663,4999,6055],()=>b(b.s=93277));module.exports=c})();