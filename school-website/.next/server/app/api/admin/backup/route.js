(()=>{var a={};a.id=8452,a.ids=[8452],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},12909:(a,b,c)=>{"use strict";c.d(b,{Yk:()=>n,_p:()=>m,bS:()=>i,eg:()=>j,nG:()=>l});var d=c(66437),e=c(85663),f=c(44999);let g=(0,d.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function h(a,b){return e.Ay.compare(a,b)}async function i(a,b){try{let{data:c,error:d}=await g.from("admin_users").select("*").eq("email",a).eq("is_active",!0).single();if(d||!c||!await h(b,c.password_hash))return null;await g.from("admin_users").update({last_login:new Date().toISOString()}).eq("id",c.id);let{password_hash:e,...f}=c;return f}catch(a){return console.error("Authentication error:",a),null}}async function j(a){try{let b=crypto.randomUUID()+"-"+Date.now()+"-"+Math.random().toString(36),c=new Date;c.setDate(c.getDate()+7);let{error:d}=await g.from("admin_sessions").insert({admin_user_id:a,session_token:b,expires_at:c.toISOString()});if(d)return console.error("Session creation error:",d),null;return b}catch(a){return console.error("Session creation error:",a),null}}async function k(a){try{let{data:b,error:c}=await g.from("admin_sessions").select(`
        *,
        admin_users (*)
      `).eq("session_token",a).gt("expires_at",new Date().toISOString()).single();if(c||!b||!b.admin_users)return null;let{password_hash:d,...e}=Array.isArray(b.admin_users)?b.admin_users[0]:b.admin_users;return e}catch(a){return console.error("Session validation error:",a),null}}async function l(){try{let a=await (0,f.UL)(),b=a.get("admin_session")?.value;if(!b)return null;return await k(b)}catch(a){return console.error("Get current admin error:",a),null}}async function m(a){try{let{error:b}=await g.from("admin_sessions").delete().eq("session_token",a);return!b}catch(a){return console.error("Logout error:",a),!1}}async function n(a,b,c,d,e,f,h,i){try{await g.from("audit_logs").insert({admin_user_id:a,action:b,table_name:c,record_id:d,old_values:e,new_values:f,ip_address:h,user_agent:i})}catch(a){console.error("Audit log error:",a)}}},27910:a=>{"use strict";a.exports=require("stream")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},40623:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{POST:()=>x});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(12909);let w=(0,c(66437).UU)("https://bqzbuhdantryrwyvhnnt.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function x(a){try{let b=await (0,v.nG)();if(!b)return u.NextResponse.json({error:"Unauthorized"},{status:401});if("super_admin"!==b.role)return u.NextResponse.json({error:"Forbidden"},{status:403});let c=new Date().toISOString();try{let[d,e,f,g,h,i,j,k]=await Promise.all([w.from("announcements").select("*").order("created_at",{ascending:!1}),w.from("faculty").select("*").order("created_at",{ascending:!1}),w.from("achievements").select("*").order("created_at",{ascending:!1}),w.from("gallery").select("*").order("created_at",{ascending:!1}),w.from("contact_submissions").select("*").order("created_at",{ascending:!1}),w.from("school_settings").select("*").order("setting_key",{ascending:!0}),w.from("admin_users").select("id, email, name, role, is_active, last_login, created_at, updated_at").order("created_at",{ascending:!1}),w.from("audit_logs").select("*").order("created_at",{ascending:!1}).limit(1e3)]),l={backup_info:{created_at:c,created_by:b.id,version:"1.0",school:"Meritorious School Ludhiana",type:"full_database_backup"},data:{announcements:d.data,faculty:e.data,achievements:f.data,gallery:g.data,contacts:h.data,settings:i.data,admin_users:j.data,audit_logs:k.data},statistics:{announcements_count:d.data?.length||0,faculty_count:e.data?.length||0,achievements_count:f.data?.length||0,gallery_count:g.data?.length||0,contacts_count:h.data?.length||0,settings_count:i.data?.length||0,admin_users_count:j.data?.length||0,audit_logs_count:k.data?.length||0}},m=a.headers.get("x-forwarded-for")||a.headers.get("x-real-ip")||"unknown",n=a.headers.get("user-agent")||"unknown";return await (0,v.Yk)(b.id,"CREATE_BACKUP","system",void 0,void 0,{backup_timestamp:c,statistics:l.statistics},m,n),u.NextResponse.json({success:!0,message:"Database backup created successfully",backup_info:l.backup_info,statistics:l.statistics})}catch(a){return console.error("Database error during backup:",a),u.NextResponse.json({error:"Failed to create database backup"},{status:500})}}catch(a){return console.error("Backup API error:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/admin/backup/route",pathname:"/api/admin/backup",filename:"route",bundlePath:"app/api/admin/backup/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/backup/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/api/admin/backup/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,5663,4999,6055],()=>b(b.s=40623));module.exports=c})();