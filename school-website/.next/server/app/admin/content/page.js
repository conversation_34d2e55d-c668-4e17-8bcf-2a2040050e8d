(()=>{var a={};a.id=9556,a.ids=[9556],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1104:(a,b,c)=>{"use strict";c.d(b,{A:()=>m});var d=c(60687),e=c(7203),f=c(41123),g=c(79801),h=c(2188),i=c(84633),j=c(55023),k=c(36293),l=c(43210);function m({content:a,onChange:b,placeholder:c="Start writing...",className:m=""}){let[n,o]=(0,l.useState)(!1),[p,q]=(0,l.useState)(""),r=(0,e.hG)({extensions:[f.A,g.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),h.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-green-600 underline hover:text-green-700"}}),i.A.configure({types:["heading","paragraph"]}),j.A,k.xJ],content:a,onUpdate:({editor:a})=>{b(a.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}}});if(!r)return null;let s=({onClick:a,isActive:b=!1,disabled:c=!1,children:e,title:f})=>(0,d.jsx)("button",{type:"button",onClick:a,disabled:c,title:f,className:`p-2 rounded text-sm font-medium transition-colors ${b?"bg-green-100 text-green-700 border border-green-300":"text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent"} ${c?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:e});return(0,d.jsxs)("div",{className:`border border-gray-300 rounded-lg overflow-hidden ${m}`,children:[(0,d.jsxs)("div",{className:"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBold().run(),isActive:r.isActive("bold"),title:"Bold",children:(0,d.jsx)("strong",{children:"B"})}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleItalic().run(),isActive:r.isActive("italic"),title:"Italic",children:(0,d.jsx)("em",{children:"I"})}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleStrike().run(),isActive:r.isActive("strike"),title:"Strikethrough",children:(0,d.jsx)("s",{children:"S"})})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:1}).run(),isActive:r.isActive("heading",{level:1}),title:"Heading 1",children:"H1"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:2}).run(),isActive:r.isActive("heading",{level:2}),title:"Heading 2",children:"H2"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:3}).run(),isActive:r.isActive("heading",{level:3}),title:"Heading 3",children:"H3"})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBulletList().run(),isActive:r.isActive("bulletList"),title:"Bullet List",children:"•"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleOrderedList().run(),isActive:r.isActive("orderedList"),title:"Numbered List",children:"1."})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("left").run(),isActive:r.isActive({textAlign:"left"}),title:"Align Left",children:"⬅"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("center").run(),isActive:r.isActive({textAlign:"center"}),title:"Align Center",children:"↔"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("right").run(),isActive:r.isActive({textAlign:"right"}),title:"Align Right",children:"➡"})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>{q(r.getAttributes("link").href||""),o(!0)},isActive:r.isActive("link"),title:"Add Link",children:"\uD83D\uDD17"}),(0,d.jsx)(s,{onClick:()=>{let a=window.prompt("Enter image URL:");a&&r.chain().focus().setImage({src:a}).run()},title:"Add Image",children:"\uD83D\uDDBC"})]}),(0,d.jsxs)("div",{className:"flex gap-1",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBlockquote().run(),isActive:r.isActive("blockquote"),title:"Quote",children:'"'}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setHorizontalRule().run(),title:"Horizontal Rule",children:"―"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().undo().run(),disabled:!r.can().chain().focus().undo().run(),title:"Undo",children:"↶"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().redo().run(),disabled:!r.can().chain().focus().redo().run(),title:"Redo",children:"↷"})]})]}),(0,d.jsxs)("div",{className:"min-h-[200px] bg-white",children:[(0,d.jsx)(e.$Z,{editor:r,className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"}),r.isEmpty&&(0,d.jsx)("div",{className:"absolute top-16 left-4 text-gray-400 pointer-events-none",children:c})]}),n&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add Link"}),(0,d.jsx)("input",{type:"url",value:p,onChange:a=>q(a.target.value),placeholder:"Enter URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 mb-4",autoFocus:!0}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,d.jsx)("button",{type:"button",onClick:()=>o(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,d.jsx)("button",{type:"button",onClick:()=>{""===p?r.chain().focus().extendMarkRange("link").unsetLink().run():r.chain().focus().extendMarkRange("link").setLink({href:p}).run(),o(!1),q("")},className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700",children:"Add Link"})]})]})})]})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32660:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/ContentManagementForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/ContentManagementForm.tsx","default")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},61522:(a,b,c)=>{Promise.resolve().then(c.bind(c,13779)),Promise.resolve().then(c.bind(c,32660))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65826:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(43210),f=c(1104);function g(){let[a,b]=(0,e.useState)({}),[c,g]=(0,e.useState)(!0),[h,i]=(0,e.useState)(!1),[j,k]=(0,e.useState)(""),[l,m]=(0,e.useState)(""),[n,o]=(0,e.useState)("mission"),p=(0,e.useMemo)(()=>[{key:"mission",label:"Mission Statement",field:"mission_statement"},{key:"vision",label:"Vision Statement",field:"vision_statement"},{key:"about",label:"About School",field:"about_school"},{key:"principal",label:"Principal Message",field:"principal_message"},{key:"history",label:"School History",field:"school_history"},{key:"values",label:"Core Values",field:"core_values"},{key:"philosophy",label:"Academic Philosophy",field:"academic_philosophy"}],[]);(0,e.useCallback)(async()=>{try{let a=await fetch("/api/admin/settings");if(a.ok){let c=await a.json(),d={};p.forEach(a=>{c[a.field]&&(d[a.field]=c[a.field].value)}),b(d)}}catch(a){console.error("Error fetching content:",a),k("Failed to load content")}finally{g(!1)}},[p]);let q=async b=>{b.preventDefault(),i(!0),k(""),m("");try{let b={settings:{}};p.forEach(c=>{let d=a[c.field]||"";b.settings[c.field]={value:d,type:"text",description:c.label,is_public:!0}});let c=await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)}),d=await c.json();c.ok?(m("Content updated successfully!"),setTimeout(()=>m(""),3e3)):k(d.error||"Failed to update content")}catch(a){k("An error occurred. Please try again.")}finally{i(!1)}};if(c)return(0,d.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,d.jsx)("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,d.jsx)("div",{className:"h-32 bg-gray-200 rounded"})]},b))})});let r=p.find(a=>a.key===n);return(0,d.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:p.map(a=>(0,d.jsx)("button",{onClick:()=>o(a.key),className:`py-4 px-1 border-b-2 font-medium text-sm ${n===a.key?"border-green-500 text-green-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:a.label},a.key))})}),(0,d.jsxs)("form",{onSubmit:q,className:"p-6",children:[j&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-6",children:j}),l&&(0,d.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm mb-6",children:l}),r&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:r.label}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:{mission:"Define the school's purpose and fundamental reason for existence.",vision:"Describe the school's aspirational future and long-term goals.",about:"Provide a comprehensive overview of the school, its facilities, and programs.",principal:"Share a welcome message from the principal to students and parents.",history:"Tell the story of how the school was founded and its journey over the years.",values:"List the core values and principles that guide the school's culture.",philosophy:"Explain the school's approach to education and learning methodology."}[r.key]||""})]}),(0,d.jsx)("div",{children:(0,d.jsx)(f.A,{content:a[r.field]||"",onChange:a=>{var c;return c=r.field,void b(b=>({...b,[c]:a}))},placeholder:`Enter ${r.label.toLowerCase()}...`,className:"min-h-[400px]"})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200 mt-8",children:[(0,d.jsx)("div",{className:"text-sm text-gray-500",children:"Changes are saved across all tabs when you click Save"}),(0,d.jsx)("button",{type:"submit",disabled:h,className:"px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:h?"Saving...":"Save All Content"})]})]})]})}},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},85082:(a,b,c)=>{Promise.resolve().then(c.bind(c,73441)),Promise.resolve().then(c.bind(c,65826))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90231:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["content",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,99010)),"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/content/page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/content/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/content/page",pathname:"/admin/content",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/content/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},99010:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(37413),e=c(39916),f=c(12909),g=c(13779),h=c(32660);async function i(){let a=await (0,f.nG)();return a||(0,e.redirect)("/admin/login"),(0,d.jsx)(g.default,{currentAdmin:a,children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Content Management"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Manage mission, vision, and other school content"})]}),(0,d.jsx)(h.default,{})]})})}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6437,5663,4999,5400,5814,1080,3319],()=>b(b.s=90231));module.exports=c})();