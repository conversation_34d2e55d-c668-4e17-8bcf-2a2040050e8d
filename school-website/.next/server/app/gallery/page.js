(()=>{var a={};a.id=6235,a.ids=[6235],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11916:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k});var d=c(60687),e=c(43210),f=c(21891),g=c(85814),h=c.n(g);function i(){return(0,d.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-bold mb-4",children:"Contact Information"}),(0,d.jsx)("p",{className:"text-gray-300 mb-2",children:"Senior Secondary Residential School"}),(0,d.jsx)("p",{className:"text-gray-300 mb-2",children:"for Meritorious Students"}),(0,d.jsx)("p",{className:"text-gray-300 mb-2",children:"Civil Lines, Ludhiana, Punjab"}),(0,d.jsx)("p",{className:"text-gray-300",children:"Government of Punjab"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-bold mb-4",children:"Quick Links"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)(h(),{href:"/about",className:"text-gray-300 hover:text-white transition-colors",children:"About Us"})}),(0,d.jsx)("li",{children:(0,d.jsx)(h(),{href:"/academics",className:"text-gray-300 hover:text-white transition-colors",children:"Academics"})}),(0,d.jsx)("li",{children:(0,d.jsx)(h(),{href:"/gallery",className:"text-gray-300 hover:text-white transition-colors",children:"Gallery"})}),(0,d.jsx)("li",{children:(0,d.jsx)(h(),{href:"/faculty",className:"text-gray-300 hover:text-white transition-colors",children:"Faculty"})}),(0,d.jsx)("li",{children:(0,d.jsx)(h(),{href:"/admissions",className:"text-gray-300 hover:text-white transition-colors",children:"Admissions"})}),(0,d.jsx)("li",{children:(0,d.jsx)(h(),{href:"/contact",className:"text-gray-300 hover:text-white transition-colors",children:"Contact"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-bold mb-4",children:"Eligibility"}),(0,d.jsxs)("ul",{className:"text-gray-300 space-y-2",children:[(0,d.jsx)("li",{children:"• Minimum 80% in Matriculation"}),(0,d.jsx)("li",{children:"• Punjab state domicile"}),(0,d.jsx)("li",{children:"• Economically backward family"}),(0,d.jsx)("li",{children:"• State-wide entrance test"})]})]})]}),(0,d.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:(0,d.jsx)("p",{className:"text-gray-400",children:"\xa9 2024 Senior Secondary Residential School for Meritorious Students. All rights reserved. Government of Punjab."})})]})})}c(51617);var j=c(30474);function k(){let[a,b]=(0,e.useState)([]),[c,g]=(0,e.useState)([]),[h,k]=(0,e.useState)("all"),[l,m]=(0,e.useState)(!0),[n,o]=(0,e.useState)(null),p="all"===h?a:a.filter(a=>a.category===h);return l?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(f.default,{}),(0,d.jsx)("section",{className:"bg-green-600 text-white py-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Gallery"}),(0,d.jsx)("p",{className:"text-xl max-w-3xl mx-auto",children:"Explore our school life through photos and memories"})]})})}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(a=>(0,d.jsx)("div",{className:"bg-gray-200 aspect-square rounded-lg animate-pulse"},a))})})})]}):n?(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(f.default,{}),(0,d.jsx)("section",{className:"bg-green-600 text-white py-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Gallery"}),(0,d.jsx)("p",{className:"text-xl max-w-3xl mx-auto",children:"Explore our school life through photos and memories"})]})})}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 text-center",children:(0,d.jsx)("p",{className:"text-red-600",children:n})})})})]}):(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)(f.default,{}),(0,d.jsx)("section",{className:"bg-green-600 text-white py-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Gallery"}),(0,d.jsx)("p",{className:"text-xl max-w-3xl mx-auto",children:"Explore our school life through photos capturing moments of learning, achievement, and joy in our vibrant educational community."})]})})}),(0,d.jsx)("section",{className:"py-8 bg-white border-b",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:c.filter(a=>a).map(a=>(0,d.jsx)("button",{onClick:()=>k(a),className:`px-6 py-2 rounded-full font-medium transition-colors ${h===a?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:a&&a.charAt(0).toUpperCase()+a.slice(1)},a))})})}),(0,d.jsx)("section",{className:"py-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:0===p.length?(0,d.jsx)("div",{className:"text-center py-12",children:(0,d.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-8",children:[(0,d.jsx)("svg",{className:"w-16 h-16 text-blue-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"No Images Found"}),(0,d.jsx)("p",{className:"text-blue-600",children:"all"===h?"Gallery images will be added soon.":`No images found in the "${h}" category.`})]})}):(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map(a=>(0,d.jsxs)("div",{className:"group relative bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow",children:[(0,d.jsxs)("div",{className:"aspect-square relative",children:[(0,d.jsx)(j.default,{src:a.image_url||"/placeholder-image.jpg",alt:a.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300"})]}),(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:a.title}),a.description&&(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:a.description}),(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsx)("span",{className:"inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:a.category.charAt(0).toUpperCase()+a.category.slice(1)})})]})]},a.id))})})}),(0,d.jsx)(i,{})]})}},11997:a=>{"use strict";a.exports=require("punycode")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23709:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/gallery/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/gallery/page.tsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39727:()=>{},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47990:()=>{},51617:(a,b,c)=>{"use strict";c.d(b,{G8:()=>j,Wq:()=>n,_6:()=>k,hD:()=>l,yl:()=>m});var d=c(60463);let e=()=>"https://bqzbuhdantryrwyvhnnt.supabase.co".includes("supabase.co"),f=()=>{if(!e())throw Error("Supabase not configured");return(0,d.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxemJ1aGRhbnRyeXJ3eXZobm50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDE1OTcsImV4cCI6MjA2ODQxNzU5N30.Eb5fqPvaSZPEVj1ATu-JO90-chl8LreyjkXdxzOd2PM")},g=[{id:"1",title:"Admission 2024-25 Open",content:"Applications are now open for admission to Class 11th for the academic year 2024-25. Eligible students can apply online through our official website.",priority:"high",status:"published",is_published:!0,publish_date:"2024-03-01T10:00:00Z",created_at:"2024-03-01T10:00:00Z",updated_at:"2024-03-01T10:00:00Z"},{id:"2",title:"Annual Sports Day",content:"Annual Sports Day will be held on March 15, 2024. All students are encouraged to participate in various sporting events.",priority:"medium",status:"published",is_published:!0,publish_date:"2024-02-20T09:00:00Z",created_at:"2024-02-20T09:00:00Z",updated_at:"2024-02-20T09:00:00Z"}],h=[{id:"1",title:"JEE Advanced Qualification",description:"Student qualified for JEE Advanced 2023 with All India Rank under 5000",student_name:"Arjun Sharma",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-06-15",award_by:"IIT Council",position:"AIR 4500",image_url:void 0,certificate_url:void 0,status:"published",is_published:!0,created_at:"2023-06-15T00:00:00Z",updated_at:"2023-06-15T00:00:00Z"},{id:"2",title:"NEET Top Scorer",description:"Achieved 650+ marks in NEET 2023, securing admission in government medical college",student_name:"Priya Kaur",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-05-20",award_by:"NTA",position:"650/720",image_url:void 0,certificate_url:void 0,status:"published",is_published:!0,created_at:"2023-05-20T00:00:00Z",updated_at:"2023-05-20T00:00:00Z"}],i=[{id:"1",name:"Dr. Rajesh Kumar",designation:"Principal",department:"Administration",qualification:"Ph.D. in Education, M.A. Education",experience_years:25,image_url:void 0,email:"<EMAIL>",phone:void 0,bio:"Dr. Rajesh Kumar brings over 25 years of experience in educational administration.",is_published:!0,order_index:1,created_at:"2024-01-01T00:00:00Z",updated_at:"2024-01-01T00:00:00Z"}];async function j(a=5){if(!e())return g.slice(0,a);try{let b=f(),{data:c,error:d}=await b.from("announcements").select("*").eq("is_published",!0).order("publish_date",{ascending:!1}).limit(a);if(d)return console.error("Error fetching announcements:",d),g.slice(0,a);return c||g.slice(0,a)}catch(b){return console.error("Supabase error:",b),g.slice(0,a)}}async function k(a){return[]}async function l(){return[]}async function m(){if(!e())return i;try{let a=f(),{data:b,error:c}=await a.from("faculty").select("*").eq("is_published",!0).order("order_index",{ascending:!0});if(c)return console.error("Error fetching faculty:",c),i;return b||i}catch(a){return console.error("Supabase error:",a),i}}async function n(a=6){if(!e())return h.slice(0,a);try{let b=f(),{data:c,error:d}=await b.from("achievements").select("*").eq("is_published",!0).order("achievement_date",{ascending:!1}).limit(a);if(d)return console.error("Error fetching achievements:",d),h.slice(0,a);return c||h.slice(0,a)}catch(b){return console.error("Supabase error:",b),h.slice(0,a)}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},62238:(a,b,c)=>{Promise.resolve().then(c.bind(c,11916))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66966:(a,b,c)=>{Promise.resolve().then(c.bind(c,23709))},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89505:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,23709)),"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/gallery/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/gallery/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/gallery/page",pathname:"/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/gallery/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,5400,5814,463,474,1195],()=>b(b.s=89505));module.exports=c})();