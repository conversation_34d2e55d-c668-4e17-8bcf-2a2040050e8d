<!DOCTYPE html><!--JFsU5ZmeFqNd9a_372p0C--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/14cff3ed71bb3e58.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-5ef0a342ac47cd1c.js"/><script src="/_next/static/chunks/4bd1b696-602635ee57868870.js" async=""></script><script src="/_next/static/chunks/5964-e98f132f9229d654.js" async=""></script><script src="/_next/static/chunks/main-app-6ecb57e1db857709.js" async=""></script><script src="/_next/static/chunks/6874-d27b54d0b28e3259.js" async=""></script><script src="/_next/static/chunks/4615-65489319b316624f.js" async=""></script><script src="/_next/static/chunks/app/search/page-35aa124c948de0c5.js" async=""></script><meta name="next-size-adjust" content=""/><title>Search - Meritorious School Ludhiana</title><meta name="description" content="Search for announcements, faculty information, achievements, and more at Meritorious School Ludhiana."/><meta name="author" content="Government of Punjab"/><meta name="keywords" content="search, announcements, faculty, achievements, Meritorious School Ludhiana"/><meta property="og:title" content="Senior Secondary Residential School for Meritorious Students"/><meta property="og:description" content="Free education and residential facilities for meritorious students in Punjab"/><meta property="og:locale" content="en_IN"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary"/><meta name="twitter:title" content="Senior Secondary Residential School for Meritorious Students"/><meta name="twitter:description" content="Free education and residential facilities for meritorious students in Punjab"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between items-center py-6"><div class="flex items-center"><div class="flex-shrink-0"><h1 class="text-2xl font-bold text-green-900">Meritorious School Ludhiana</h1><p class="text-sm text-gray-600">Government of Punjab</p></div></div><nav class="hidden md:block"><div class="ml-10 flex items-baseline space-x-4"><a class="text-green-900 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium" href="/">Home</a><a class="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium" href="/about">About</a><a class="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium" href="/academics">Academics</a><a class="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium" href="/gallery">Gallery</a><a class="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium" href="/faculty">Faculty</a><a class="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium" href="/admissions">Admissions</a><a class="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium" href="/contact">Contact</a></div></nav><div class="hidden md:block ml-6"><div class="relative w-64"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg></div><input type="text" placeholder="Search..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" value=""/></div></div></div><div class="md:hidden"><button class="text-gray-600 hover:text-green-700 focus:outline-none focus:text-green-700"><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></div></div></header><main class="py-12"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-12"><h1 class="text-4xl font-bold text-gray-900 mb-4">Search Our Content</h1><p class="text-xl text-gray-600 max-w-3xl mx-auto">Find announcements, faculty information, student achievements, and more across our website.</p></div><div class="max-w-4xl mx-auto"><div class="relative w-full"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg></div><input type="text" placeholder="Search announcements, faculty, achievements..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500" value=""/></div><div class="mt-2 flex flex-wrap gap-2"><button class="px-3 py-1 text-sm rounded-full transition-colors bg-green-100 text-green-700 border border-green-300">All Content</button><button class="px-3 py-1 text-sm rounded-full transition-colors bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300">Announcements</button><button class="px-3 py-1 text-sm rounded-full transition-colors bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300">Faculty</button><button class="px-3 py-1 text-sm rounded-full transition-colors bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300">Achievements</button></div></div></div><div class="max-w-4xl mx-auto mt-12"><div class="bg-white rounded-lg shadow-md p-8"><h2 class="text-2xl font-bold text-gray-900 mb-6">Search Tips</h2><div class="grid md:grid-cols-2 gap-8"><div><h3 class="text-lg font-semibold text-gray-900 mb-4">What you can search for:</h3><ul class="space-y-2 text-gray-600"><li class="flex items-center"><span class="text-green-600 mr-2">📢</span><span><strong>Announcements:</strong> School news, events, and updates</span></li><li class="flex items-center"><span class="text-green-600 mr-2">👨‍🏫</span><span><strong>Faculty:</strong> Teacher profiles, qualifications, and specializations</span></li><li class="flex items-center"><span class="text-green-600 mr-2">🏆</span><span><strong>Achievements:</strong> Student accomplishments and awards</span></li></ul></div><div><h3 class="text-lg font-semibold text-gray-900 mb-4">Search tips:</h3><ul class="space-y-2 text-gray-600"><li>• Use specific keywords for better results</li><li>• Try different spellings if you don&#x27;t find what you&#x27;re looking for</li><li>• Use the content type filters to narrow your search</li><li>• Search for student names to find their achievements</li><li>• Look for teacher names or subjects to find faculty information</li></ul></div></div></div></div><div class="max-w-4xl mx-auto mt-8"><div class="bg-white rounded-lg shadow-md p-8"><h2 class="text-2xl font-bold text-gray-900 mb-6">Popular Searches</h2><div class="flex flex-wrap gap-3"><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">JEE Advanced</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">NEET results</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">Sports achievements</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">Science Olympiad</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">Mathematics teacher</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">Physics faculty</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">Cultural events</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">Academic calendar</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">Admission process</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors">School events</span></div></div></div></div></main><footer class="bg-gray-900 text-white py-12"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="grid md:grid-cols-3 gap-8"><div><h3 class="text-xl font-bold mb-4">Contact Information</h3><p class="text-gray-300 mb-2">Senior Secondary Residential School</p><p class="text-gray-300 mb-2">for Meritorious Students</p><p class="text-gray-300 mb-2">Civil Lines, Ludhiana, Punjab</p><p class="text-gray-300">Government of Punjab</p></div><div><h3 class="text-xl font-bold mb-4">Quick Links</h3><ul class="space-y-2"><li><a class="text-gray-300 hover:text-white transition-colors" href="/about">About Us</a></li><li><a class="text-gray-300 hover:text-white transition-colors" href="/academics">Academics</a></li><li><a class="text-gray-300 hover:text-white transition-colors" href="/gallery">Gallery</a></li><li><a class="text-gray-300 hover:text-white transition-colors" href="/faculty">Faculty</a></li><li><a class="text-gray-300 hover:text-white transition-colors" href="/admissions">Admissions</a></li><li><a class="text-gray-300 hover:text-white transition-colors" href="/contact">Contact</a></li></ul></div><div><h3 class="text-xl font-bold mb-4">Eligibility</h3><ul class="text-gray-300 space-y-2"><li>• Minimum 80% in Matriculation</li><li>• Punjab state domicile</li><li>• Economically backward family</li><li>• State-wide entrance test</li></ul></div></div><div class="border-t border-gray-800 mt-8 pt-8 text-center"><p class="text-gray-400">© 2024 Senior Secondary Residential School for Meritorious Students. All rights reserved. Government of Punjab.</p></div></div></footer></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-5ef0a342ac47cd1c.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[4615,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4615\",\"static/chunks/4615-65489319b316624f.js\",\"2959\",\"static/chunks/app/search/page-35aa124c948de0c5.js\"],\"default\"]\n5:I[8337,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4615\",\"static/chunks/4615-65489319b316624f.js\",\"2959\",\"static/chunks/app/search/page-35aa124c948de0c5.js\"],\"default\"]\nc:I[8393,[],\"\"]\n:HL[\"/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/14cff3ed71bb3e58.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"JFsU5ZmeFqNd9a_372p0C\",\"p\":\"\",\"c\":[\"\",\"search\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"search\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/14cff3ed71bb3e58.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"search\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[[\"$\",\"$L4\",null,{}],[\"$\",\"main\",null,{\"className\":\"py-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-center mb-12\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-4xl font-bold text-gray-900 mb-4\",\"children\":\"Search Our Content\"}],[\"$\",\"p\",null,{\"className\":\"text-xl text-gray-600 max-w-3xl mx-auto\",\"children\":\"Find announcements, faculty information, student achievements, and more across our website.\"}]]}],[\"$\",\"div\",null,{\"className\":\"max-w-4xl mx-auto\",\"children\":[\"$\",\"$L5\",null,{\"placeholder\":\"Search announcements, faculty, achievements...\",\"className\":\"w-full\",\"showFilters\":true}]}],[\"$\",\"div\",null,{\"className\":\"max-w-4xl mx-auto mt-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow-md p-8\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-2xl font-bold text-gray-900 mb-6\",\"children\":\"Search Tips\"}],[\"$\",\"div\",null,{\"className\":\"grid md:grid-cols-2 gap-8\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-900 mb-4\",\"children\":\"What you can search for:\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-gray-600\",\"children\":[[\"$\",\"li\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-green-600 mr-2\",\"children\":\"📢\"}],[\"$\",\"span\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Announcements:\"}],\" School news, events, and updates\"]}]]}],[\"$\",\"li\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-green-600 mr-2\",\"children\":\"👨‍🏫\"}],[\"$\",\"span\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Faculty:\"}],\" Teacher profiles, qualifications, and specializations\"]}]]}],[\"$\",\"li\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-green-600 mr-2\",\"children\":\"🏆\"}],[\"$\",\"span\",null,{\"children\":[[\"$\",\"strong\",null,{\"children\":\"Achievements:\"}],\" Student accomplishments and awards\"]}]]}]]}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold text-gray-900 mb-4\",\"children\":\"Search tips:\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2 text-gray-600\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• Use specific keywords for better results\"}],[\"$\",\"li\",null,{\"children\":\"• Try different spellings if you don't find what you're looking for\"}],[\"$\",\"li\",null,{\"children\":\"• Use the content type filters to narrow your search\"}],\"$L6\",\"$L7\"]}]]}]]}]]}]}],\"$L8\"]}]}],\"$L9\"]}],null,\"$La\"]}],{},null,false]},null,false]},null,false],\"$Lb\",false]],\"m\":\"$undefined\",\"G\":[\"$c\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"d:I[6874,[\"6874\",\"static/chunks/6874-d27b54d0b28e3259.js\",\"4615\",\"static/chunks/4615-65489319b316624f.js\",\"2959\",\"static/chunks/app/search/page-35aa124c948de0c5.js\"],\"\"]\ne:I[9665,[],\"OutletBoundary\"]\n10:I[4911,[],\"AsyncMetadataOutlet\"]\n12:I[9665,[],\"ViewportBoundary\"]\n14:I[9665,[],\"MetadataBoundary\"]\n15:\"$Sreact.suspense\"\n6:[\"$\",\"li\",null,{\"children\":\"• Search for student names to find their achievements\"}]\n7:[\"$\",\"li\",null,{\"children\":\"• Look for teacher names or subjects to find faculty information\"}]\n"])</script><script>self.__next_f.push([1,"8:[\"$\",\"div\",null,{\"className\":\"max-w-4xl mx-auto mt-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow-md p-8\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-2xl font-bold text-gray-900 mb-6\",\"children\":\"Popular Searches\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-3\",\"children\":[[\"$\",\"span\",\"JEE Advanced\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"JEE Advanced\"}],[\"$\",\"span\",\"NEET results\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"NEET results\"}],[\"$\",\"span\",\"Sports achievements\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"Sports achievements\"}],[\"$\",\"span\",\"Science Olympiad\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"Science Olympiad\"}],[\"$\",\"span\",\"Mathematics teacher\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"Mathematics teacher\"}],[\"$\",\"span\",\"Physics faculty\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"Physics faculty\"}],[\"$\",\"span\",\"Cultural events\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"Cultural events\"}],[\"$\",\"span\",\"Academic calendar\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"Academic calendar\"}],[\"$\",\"span\",\"Admission process\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"Admission process\"}],[\"$\",\"span\",\"School events\",{\"className\":\"px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors\",\"children\":\"School events\"}]]}]]}]}]\n"])</script><script>self.__next_f.push([1,"9:[\"$\",\"footer\",null,{\"className\":\"bg-gray-900 text-white py-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grid md:grid-cols-3 gap-8\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-xl font-bold mb-4\",\"children\":\"Contact Information\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-300 mb-2\",\"children\":\"Senior Secondary Residential School\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-300 mb-2\",\"children\":\"for Meritorious Students\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-300 mb-2\",\"children\":\"Civil Lines, Ludhiana, Punjab\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-300\",\"children\":\"Government of Punjab\"}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-xl font-bold mb-4\",\"children\":\"Quick Links\"}],[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$Ld\",null,{\"href\":\"/about\",\"className\":\"text-gray-300 hover:text-white transition-colors\",\"children\":\"About Us\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Ld\",null,{\"href\":\"/academics\",\"className\":\"text-gray-300 hover:text-white transition-colors\",\"children\":\"Academics\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Ld\",null,{\"href\":\"/gallery\",\"className\":\"text-gray-300 hover:text-white transition-colors\",\"children\":\"Gallery\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Ld\",null,{\"href\":\"/faculty\",\"className\":\"text-gray-300 hover:text-white transition-colors\",\"children\":\"Faculty\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Ld\",null,{\"href\":\"/admissions\",\"className\":\"text-gray-300 hover:text-white transition-colors\",\"children\":\"Admissions\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$Ld\",null,{\"href\":\"/contact\",\"className\":\"text-gray-300 hover:text-white transition-colors\",\"children\":\"Contact\"}]}]]}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-xl font-bold mb-4\",\"children\":\"Eligibility\"}],[\"$\",\"ul\",null,{\"className\":\"text-gray-300 space-y-2\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• Minimum 80% in Matriculation\"}],[\"$\",\"li\",null,{\"children\":\"• Punjab state domicile\"}],[\"$\",\"li\",null,{\"children\":\"• Economically backward family\"}],[\"$\",\"li\",null,{\"children\":\"• State-wide entrance test\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"border-t border-gray-800 mt-8 pt-8 text-center\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-gray-400\",\"children\":\"© 2024 Senior Secondary Residential School for Meritorious Students. All rights reserved. Government of Punjab.\"}]}]]}]}]\n"])</script><script>self.__next_f.push([1,"a:[\"$\",\"$Le\",null,{\"children\":[\"$Lf\",[\"$\",\"$L10\",null,{\"promise\":\"$@11\"}]]}]\nb:[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L12\",null,{\"children\":\"$L13\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]],[\"$\",\"$L14\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$15\",null,{\"fallback\":null,\"children\":\"$L16\"}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"13:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nf:null\n"])</script><script>self.__next_f.push([1,"17:I[8175,[],\"IconMark\"]\n"])</script><script>self.__next_f.push([1,"11:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Search - Meritorious School Ludhiana\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Search for announcements, faculty information, achievements, and more at Meritorious School Ludhiana.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Government of Punjab\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"search, announcements, faculty, achievements, Meritorious School Ludhiana\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:title\",\"content\":\"Senior Secondary Residential School for Meritorious Students\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:description\",\"content\":\"Free education and residential facilities for meritorious students in Punjab\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:locale\",\"content\":\"en_IN\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"8\",{\"name\":\"twitter:card\",\"content\":\"summary\"}],[\"$\",\"meta\",\"9\",{\"name\":\"twitter:title\",\"content\":\"Senior Secondary Residential School for Meritorious Students\"}],[\"$\",\"meta\",\"10\",{\"name\":\"twitter:description\",\"content\":\"Free education and residential facilities for meritorious students in Punjab\"}],[\"$\",\"link\",\"11\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L17\",\"12\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"16:\"$11:metadata\"\n"])</script></body></html>