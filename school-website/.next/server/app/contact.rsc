1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[4615,["6874","static/chunks/6874-d27b54d0b28e3259.js","4615","static/chunks/4615-65489319b316624f.js","977","static/chunks/app/contact/page-d61b284844ca39b0.js"],"default"]
d:I[8393,[],""]
:HL["/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/_next/static/css/14cff3ed71bb3e58.css","style"]
0:{"P":null,"b":"JFsU5ZmeFqNd9a_372p0C","p":"","c":["","contact"],"i":false,"f":[[["",{"children":["contact",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/14cff3ed71bb3e58.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]]}],{"children":["contact",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50","children":[["$","$L4",null,{}],["$","section",null,{"className":"bg-green-600 text-white py-16","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"text-center","children":[["$","h1",null,{"className":"text-4xl md:text-5xl font-bold mb-6","children":"Contact Us"}],["$","p",null,{"className":"text-xl max-w-3xl mx-auto","children":"We're here to help you with any questions about admissions, academics, or our residential facilities. Get in touch with us today."}]]}]}]}],["$","section",null,{"className":"py-16","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":["$","div",null,{"className":"grid lg:grid-cols-2 gap-12","children":[["$","div",null,{"children":[["$","h2",null,{"className":"text-3xl font-bold text-gray-900 mb-8","children":"Get in Touch"}],["$","div",null,{"className":"space-y-6","children":[["$","div",null,{"className":"flex items-start","children":[["$","div",null,{"className":"flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center","children":["$","svg",null,{"className":"w-6 h-6 text-green-600","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":[["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}],["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M15 11a3 3 0 11-6 0 3 3 0 016 0z"}]]}]}],["$","div",null,{"className":"ml-4","children":[["$","h3",null,{"className":"text-lg font-semibold text-gray-900","children":"Address"}],["$","p",null,{"className":"text-gray-600 mt-1","children":["Senior Secondary Residential School",["$","br",null,{}],"for Meritorious Students",["$","br",null,{}],"Civil Lines, Ludhiana",["$","br",null,{}],"Punjab, India - 141001"]}]]}]]}],["$","div",null,{"className":"flex items-start","children":[["$","div",null,{"className":"flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center","children":["$","svg",null,{"className":"w-6 h-6 text-green-600","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"}]}]}],"$L5"]}],"$L6","$L7"]}]]}],"$L8"]}]}]}],"$L9","$La"]}],null,"$Lb"]}],{},null,false]},null,false]},null,false],"$Lc",false]],"m":"$undefined","G":["$d",[]],"s":false,"S":true}
e:I[5118,["6874","static/chunks/6874-d27b54d0b28e3259.js","4615","static/chunks/4615-65489319b316624f.js","977","static/chunks/app/contact/page-d61b284844ca39b0.js"],"default"]
f:I[6874,["6874","static/chunks/6874-d27b54d0b28e3259.js","4615","static/chunks/4615-65489319b316624f.js","977","static/chunks/app/contact/page-d61b284844ca39b0.js"],""]
10:I[9665,[],"OutletBoundary"]
12:I[4911,[],"AsyncMetadataOutlet"]
14:I[9665,[],"ViewportBoundary"]
16:I[9665,[],"MetadataBoundary"]
17:"$Sreact.suspense"
5:["$","div",null,{"className":"ml-4","children":[["$","h3",null,{"className":"text-lg font-semibold text-gray-900","children":"Phone"}],["$","p",null,{"className":"text-gray-600 mt-1","children":["Office: +91-161-2345678",["$","br",null,{}],"Principal: +91-161-2345679",["$","br",null,{}],"Admissions: +91-161-2345680"]}]]}]
6:["$","div",null,{"className":"flex items-start","children":[["$","div",null,{"className":"flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center","children":["$","svg",null,{"className":"w-6 h-6 text-green-600","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"}]}]}],["$","div",null,{"className":"ml-4","children":[["$","h3",null,{"className":"text-lg font-semibold text-gray-900","children":"Email"}],["$","p",null,{"className":"text-gray-600 mt-1","children":["General: <EMAIL>",["$","br",null,{}],"Admissions: <EMAIL>",["$","br",null,{}],"Principal: <EMAIL>"]}]]}]]}]
7:["$","div",null,{"className":"flex items-start","children":[["$","div",null,{"className":"flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center","children":["$","svg",null,{"className":"w-6 h-6 text-green-600","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"}]}]}],["$","div",null,{"className":"ml-4","children":[["$","h3",null,{"className":"text-lg font-semibold text-gray-900","children":"Office Hours"}],["$","p",null,{"className":"text-gray-600 mt-1","children":["Monday - Friday: 9:00 AM - 5:00 PM",["$","br",null,{}],"Saturday: 9:00 AM - 1:00 PM",["$","br",null,{}],"Sunday: Closed"]}]]}]]}]
8:["$","$Le",null,{}]
9:["$","section",null,{"className":"py-16 bg-white","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":[["$","div",null,{"className":"text-center mb-12","children":[["$","h2",null,{"className":"text-3xl font-bold text-gray-900 mb-4","children":"Find Us"}],["$","p",null,{"className":"text-lg text-gray-600","children":"Located in the heart of Civil Lines, Ludhiana"}]]}],["$","div",null,{"className":"bg-gray-200 h-96 rounded-lg flex items-center justify-center","children":["$","div",null,{"className":"text-center","children":[["$","svg",null,{"className":"w-16 h-16 text-gray-400 mx-auto mb-4","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":[["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}],["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M15 11a3 3 0 11-6 0 3 3 0 016 0z"}]]}],["$","p",null,{"className":"text-gray-600","children":"Interactive map will be integrated here"}],["$","p",null,{"className":"text-sm text-gray-500 mt-2","children":"Civil Lines, Ludhiana, Punjab"}]]}]}]]}]}]
a:["$","footer",null,{"className":"bg-gray-900 text-white py-12","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8","children":[["$","div",null,{"className":"grid md:grid-cols-3 gap-8","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-bold mb-4","children":"Contact Information"}],["$","p",null,{"className":"text-gray-300 mb-2","children":"Senior Secondary Residential School"}],["$","p",null,{"className":"text-gray-300 mb-2","children":"for Meritorious Students"}],["$","p",null,{"className":"text-gray-300 mb-2","children":"Civil Lines, Ludhiana, Punjab"}],["$","p",null,{"className":"text-gray-300","children":"Government of Punjab"}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-bold mb-4","children":"Quick Links"}],["$","ul",null,{"className":"space-y-2","children":[["$","li",null,{"children":["$","$Lf",null,{"href":"/about","className":"text-gray-300 hover:text-white transition-colors","children":"About Us"}]}],["$","li",null,{"children":["$","$Lf",null,{"href":"/academics","className":"text-gray-300 hover:text-white transition-colors","children":"Academics"}]}],["$","li",null,{"children":["$","$Lf",null,{"href":"/gallery","className":"text-gray-300 hover:text-white transition-colors","children":"Gallery"}]}],["$","li",null,{"children":["$","$Lf",null,{"href":"/faculty","className":"text-gray-300 hover:text-white transition-colors","children":"Faculty"}]}],["$","li",null,{"children":["$","$Lf",null,{"href":"/admissions","className":"text-gray-300 hover:text-white transition-colors","children":"Admissions"}]}],["$","li",null,{"children":["$","$Lf",null,{"href":"/contact","className":"text-gray-300 hover:text-white transition-colors","children":"Contact"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-xl font-bold mb-4","children":"Eligibility"}],["$","ul",null,{"className":"text-gray-300 space-y-2","children":[["$","li",null,{"children":"• Minimum 80% in Matriculation"}],["$","li",null,{"children":"• Punjab state domicile"}],["$","li",null,{"children":"• Economically backward family"}],["$","li",null,{"children":"• State-wide entrance test"}]]}]]}]]}],["$","div",null,{"className":"border-t border-gray-800 mt-8 pt-8 text-center","children":["$","p",null,{"className":"text-gray-400","children":"© 2024 Senior Secondary Residential School for Meritorious Students. All rights reserved. Government of Punjab."}]}]]}]}]
b:["$","$L10",null,{"children":["$L11",["$","$L12",null,{"promise":"$@13"}]]}]
c:["$","$1","h",{"children":[null,[["$","$L14",null,{"children":"$L15"}],["$","meta",null,{"name":"next-size-adjust","content":""}]],["$","$L16",null,{"children":["$","div",null,{"hidden":true,"children":["$","$17",null,{"fallback":null,"children":"$L18"}]}]}]]}]
15:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
11:null
19:I[8175,[],"IconMark"]
13:{"metadata":[["$","title","0",{"children":"Contact Us - Senior Secondary Residential School for Meritorious Students"}],["$","meta","1",{"name":"description","content":"Get in touch with us. Located at Civil Lines, Ludhiana, Punjab. Contact details, address, and inquiry form for admissions and general information."}],["$","meta","2",{"name":"author","content":"Government of Punjab"}],["$","meta","3",{"name":"keywords","content":"meritorious school, ludhiana, government of punjab, free education, residential school, JEE coaching, NEET coaching, admission 2024"}],["$","meta","4",{"property":"og:title","content":"Senior Secondary Residential School for Meritorious Students"}],["$","meta","5",{"property":"og:description","content":"Free education and residential facilities for meritorious students in Punjab"}],["$","meta","6",{"property":"og:locale","content":"en_IN"}],["$","meta","7",{"property":"og:type","content":"website"}],["$","meta","8",{"name":"twitter:card","content":"summary"}],["$","meta","9",{"name":"twitter:title","content":"Senior Secondary Residential School for Meritorious Students"}],["$","meta","10",{"name":"twitter:description","content":"Free education and residential facilities for meritorious students in Punjab"}],["$","link","11",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L19","12",{}]],"error":null,"digest":"$undefined"}
18:"$13:metadata"
