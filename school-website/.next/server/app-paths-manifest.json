{"/_not-found/page": "app/_not-found/page.js", "/api/admin/achievements/[id]/route": "app/api/admin/achievements/[id]/route.js", "/api/admin/announcements/[id]/route": "app/api/admin/announcements/[id]/route.js", "/api/admin/achievements/route": "app/api/admin/achievements/route.js", "/api/admin/announcements/route": "app/api/admin/announcements/route.js", "/api/admin/auth/login/route": "app/api/admin/auth/login/route.js", "/api/admin/audit/route": "app/api/admin/audit/route.js", "/api/admin/backup/route": "app/api/admin/backup/route.js", "/api/admin/auth/logout/route": "app/api/admin/auth/logout/route.js", "/api/admin/contacts/[id]/route": "app/api/admin/contacts/[id]/route.js", "/api/admin/faculty/route": "app/api/admin/faculty/route.js", "/api/admin/faculty/[id]/route": "app/api/admin/faculty/[id]/route.js", "/api/admin/export/route": "app/api/admin/export/route.js", "/api/admin/contacts/route": "app/api/admin/contacts/route.js", "/api/admin/gallery/bulk/route": "app/api/admin/gallery/bulk/route.js", "/api/admin/gallery/route": "app/api/admin/gallery/route.js", "/api/admin/gallery/[id]/route": "app/api/admin/gallery/[id]/route.js", "/api/admin/scheduled-publish/route": "app/api/admin/scheduled-publish/route.js", "/api/admin/init-db/route": "app/api/admin/init-db/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/admin/upload/route": "app/api/admin/upload/route.js", "/api/contact/route": "app/api/contact/route.js", "/api/settings/route": "app/api/settings/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/search/route": "app/api/search/route.js", "/admin/achievements/page": "app/admin/achievements/page.js", "/admin/achievements/[id]/edit/page": "app/admin/achievements/[id]/edit/page.js", "/admin/achievements/new/page": "app/admin/achievements/new/page.js", "/admin/announcements/new/page": "app/admin/announcements/new/page.js", "/admin/announcements/[id]/edit/page": "app/admin/announcements/[id]/edit/page.js", "/admin/audit/page": "app/admin/audit/page.js", "/admin/announcements/page": "app/admin/announcements/page.js", "/admin/content/page": "app/admin/content/page.js", "/admin/backup/page": "app/admin/backup/page.js", "/admin/dashboard/page": "app/admin/dashboard/page.js", "/admin/faculty/[id]/edit/page": "app/admin/faculty/[id]/edit/page.js", "/admin/gallery/page": "app/admin/gallery/page.js", "/admin/faculty/new/page": "app/admin/faculty/new/page.js", "/admin/login/page": "app/admin/login/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/faculty/page": "app/admin/faculty/page.js", "/admin/gallery/upload/page": "app/admin/gallery/upload/page.js", "/admin/setup/page": "app/admin/setup/page.js", "/gallery/page": "app/gallery/page.js", "/page": "app/page.js", "/about/page": "app/about/page.js", "/academics/page": "app/academics/page.js", "/admissions/page": "app/admissions/page.js", "/contact/page": "app/contact/page.js", "/faculty/page": "app/faculty/page.js", "/search/page": "app/search/page.js"}