exports.id=3319,exports.ids=[3319],exports.modules={12909:(a,b,c)=>{"use strict";c.d(b,{Yk:()=>m,_p:()=>l,bS:()=>h,eg:()=>i,nG:()=>k});var d=c(85663),e=c(44999),f=c(56621);async function g(a,b){return d.Ay.compare(a,b)}async function h(a,b){try{let c=(0,f.LE)(),{data:d,error:e}=await c.from("admin_users").select("*").eq("email",a).eq("is_active",!0).single();if(e||!d||!await g(b,d.password_hash))return null;await c.from("admin_users").update({last_login:new Date().toISOString()}).eq("id",d.id);let{password_hash:h,...i}=d;return i}catch(a){return console.error("Authentication error:",a),null}}async function i(a){try{let b=(0,f.LE)(),c=crypto.randomUUID()+"-"+Date.now()+"-"+Math.random().toString(36),d=new Date;d.setDate(d.getDate()+7);let{error:e}=await b.from("admin_sessions").insert({admin_user_id:a,session_token:c,expires_at:d.toISOString()});if(e)return console.error("Session creation error:",e),null;return c}catch(a){return console.error("Session creation error:",a),null}}async function j(a){try{let b=(0,f.LE)(),{data:c,error:d}=await b.from("admin_sessions").select(`
        *,
        admin_users (*)
      `).eq("session_token",a).gt("expires_at",new Date().toISOString()).single();if(d||!c||!c.admin_users)return null;let{password_hash:e,...g}=Array.isArray(c.admin_users)?c.admin_users[0]:c.admin_users;return g}catch(a){return console.error("Session validation error:",a),null}}async function k(){try{let a=await (0,e.UL)(),b=a.get("admin_session")?.value;if(!b)return null;return await j(b)}catch(a){return console.error("Get current admin error:",a),null}}async function l(a){try{let b=(0,f.LE)(),{error:c}=await b.from("admin_sessions").delete().eq("session_token",a);return!c}catch(a){return console.error("Logout error:",a),!1}}async function m(a,b,c,d,e,g,h,i){try{let j=(0,f.LE)();await j.from("audit_logs").insert({admin_user_id:a,action:b,table_name:c,record_id:d,old_values:e,new_values:g,ip_address:h,user_agent:i})}catch(a){console.error("Audit log error:",a)}}},13779:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AdminLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AdminLayout.tsx","default")},25488:()=>{},35216:()=>{},39727:()=>{},47990:()=>{},51176:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},56621:(a,b,c)=>{"use strict";c.d(b,{LE:()=>e});var d=c(66437);let e=()=>(0,d.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}});(0,d.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxemJ1aGRhbnRyeXJ3eXZobm50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDE1OTcsImV4cCI6MjA2ODQxNzU5N30.Eb5fqPvaSZPEVj1ATu-JO90-chl8LreyjkXdxzOd2PM"),e()},61135:()=>{},64328:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},73441:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(16189);function i({children:a,currentAdmin:b}){let[c,f]=(0,e.useState)(!1),i=(0,h.useRouter)(),j=async()=>{try{await fetch("/api/admin/auth/logout",{method:"POST"}),i.push("/admin/login")}catch(a){console.error("Logout error:",a)}},k=[{name:"Dashboard",href:"/admin/dashboard",icon:"dashboard"},{name:"Announcements",href:"/admin/announcements",icon:"announcements"},{name:"Faculty",href:"/admin/faculty",icon:"faculty"},{name:"Gallery",href:"/admin/gallery",icon:"gallery"},{name:"Achievements",href:"/admin/achievements",icon:"trophy"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Users",href:"/admin/users",icon:"users",adminOnly:!0},{name:"Audit Logs",href:"/admin/audit",icon:"audit",adminOnly:!0},{name:"Backup",href:"/admin/backup",icon:"backup",adminOnly:!0},{name:"Settings",href:"/admin/settings",icon:"settings"},{name:"Achievements",href:"/admin/achievements",icon:"achievements"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Settings",href:"/admin/settings",icon:"settings"}],l=a=>{let b={dashboard:(0,d.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"})]}),announcements:(0,d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"})}),faculty:(0,d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),gallery:(0,d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),achievements:(0,d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),content:(0,d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),settings:(0,d.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})};return b[a]||b.dashboard};return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,d.jsxs)("div",{className:`fixed inset-0 flex z-40 md:hidden ${c?"":"hidden"}`,children:[(0,d.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>f(!1)}),(0,d.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,d.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,d.jsx)("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>f(!1),children:(0,d.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})}),(0,d.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,d.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,d.jsx)("h1",{className:"text-lg font-semibold text-green-600",children:"Admin Panel"})}),(0,d.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:k.filter(a=>!a.adminOnly||"super_admin"===b.role).map(a=>(0,d.jsxs)(g(),{href:a.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium rounded-md",children:[l(a.icon),(0,d.jsx)("span",{className:"ml-3",children:a.name})]},a.name))})]})]})]}),(0,d.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,d.jsx)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:(0,d.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,d.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,d.jsx)("h1",{className:"text-xl font-bold text-green-600",children:"Admin Panel"})}),(0,d.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:k.filter(a=>!a.adminOnly||"super_admin"===b.role).map(a=>(0,d.jsxs)(g(),{href:a.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:[l(a.icon),(0,d.jsx)("span",{className:"ml-3",children:a.name})]},a.name))})]})})}),(0,d.jsxs)("div",{className:"md:pl-64 flex flex-col flex-1",children:[(0,d.jsx)("div",{className:"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100",children:(0,d.jsx)("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500",onClick:()=>f(!0),children:(0,d.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,d.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between h-16",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Meritorious School Admin"})}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)(g(),{href:"/",className:"text-gray-500 hover:text-gray-700 text-sm",target:"_blank",children:"View Website"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-700",children:b.name}),(0,d.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:b.role}),(0,d.jsx)("button",{onClick:j,className:"text-gray-500 hover:text-gray-700 text-sm",children:"Logout"})]})]})]})})}),(0,d.jsx)("main",{className:"flex-1",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:a})})]})]})}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(2202),f=c.n(e),g=c(64988),h=c.n(g);c(61135);let i={title:"Senior Secondary Residential School for Meritorious Students - Ludhiana",description:"Government of Punjab initiative providing free education, coaching, food, and accommodation to meritorious students from economically backward families. Established 2014 in Civil Lines, Ludhiana.",keywords:"meritorious school, ludhiana, government of punjab, free education, residential school, JEE coaching, NEET coaching, admission 2024",authors:[{name:"Government of Punjab"}],openGraph:{title:"Senior Secondary Residential School for Meritorious Students",description:"Free education and residential facilities for meritorious students in Punjab",type:"website",locale:"en_IN"}};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}}};