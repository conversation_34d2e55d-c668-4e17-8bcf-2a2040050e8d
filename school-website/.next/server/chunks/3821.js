exports.id=3821,exports.ids=[3821],exports.modules={1104:(a,b,c)=>{"use strict";c.d(b,{A:()=>m});var d=c(60687),e=c(7203),f=c(41123),g=c(79801),h=c(2188),i=c(84633),j=c(55023),k=c(36293),l=c(43210);function m({content:a,onChange:b,placeholder:c="Start writing...",className:m=""}){let[n,o]=(0,l.useState)(!1),[p,q]=(0,l.useState)(""),r=(0,e.hG)({extensions:[f.A,g.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),h.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-green-600 underline hover:text-green-700"}}),i.A.configure({types:["heading","paragraph"]}),j.A,k.xJ],content:a,onUpdate:({editor:a})=>{b(a.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}}});if(!r)return null;let s=({onClick:a,isActive:b=!1,disabled:c=!1,children:e,title:f})=>(0,d.jsx)("button",{type:"button",onClick:a,disabled:c,title:f,className:`p-2 rounded text-sm font-medium transition-colors ${b?"bg-green-100 text-green-700 border border-green-300":"text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent"} ${c?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:e});return(0,d.jsxs)("div",{className:`border border-gray-300 rounded-lg overflow-hidden ${m}`,children:[(0,d.jsxs)("div",{className:"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBold().run(),isActive:r.isActive("bold"),title:"Bold",children:(0,d.jsx)("strong",{children:"B"})}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleItalic().run(),isActive:r.isActive("italic"),title:"Italic",children:(0,d.jsx)("em",{children:"I"})}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleStrike().run(),isActive:r.isActive("strike"),title:"Strikethrough",children:(0,d.jsx)("s",{children:"S"})})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:1}).run(),isActive:r.isActive("heading",{level:1}),title:"Heading 1",children:"H1"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:2}).run(),isActive:r.isActive("heading",{level:2}),title:"Heading 2",children:"H2"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:3}).run(),isActive:r.isActive("heading",{level:3}),title:"Heading 3",children:"H3"})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBulletList().run(),isActive:r.isActive("bulletList"),title:"Bullet List",children:"•"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleOrderedList().run(),isActive:r.isActive("orderedList"),title:"Numbered List",children:"1."})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("left").run(),isActive:r.isActive({textAlign:"left"}),title:"Align Left",children:"⬅"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("center").run(),isActive:r.isActive({textAlign:"center"}),title:"Align Center",children:"↔"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("right").run(),isActive:r.isActive({textAlign:"right"}),title:"Align Right",children:"➡"})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>{q(r.getAttributes("link").href||""),o(!0)},isActive:r.isActive("link"),title:"Add Link",children:"\uD83D\uDD17"}),(0,d.jsx)(s,{onClick:()=>{let a=window.prompt("Enter image URL:");a&&r.chain().focus().setImage({src:a}).run()},title:"Add Image",children:"\uD83D\uDDBC"})]}),(0,d.jsxs)("div",{className:"flex gap-1",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBlockquote().run(),isActive:r.isActive("blockquote"),title:"Quote",children:'"'}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setHorizontalRule().run(),title:"Horizontal Rule",children:"―"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().undo().run(),disabled:!r.can().chain().focus().undo().run(),title:"Undo",children:"↶"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().redo().run(),disabled:!r.can().chain().focus().redo().run(),title:"Redo",children:"↷"})]})]}),(0,d.jsxs)("div",{className:"min-h-[200px] bg-white",children:[(0,d.jsx)(e.$Z,{editor:r,className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"}),r.isEmpty&&(0,d.jsx)("div",{className:"absolute top-16 left-4 text-gray-400 pointer-events-none",children:c})]}),n&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add Link"}),(0,d.jsx)("input",{type:"url",value:p,onChange:a=>q(a.target.value),placeholder:"Enter URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 mb-4",autoFocus:!0}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,d.jsx)("button",{type:"button",onClick:()=>o(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,d.jsx)("button",{type:"button",onClick:()=>{""===p?r.chain().focus().extendMarkRange("link").unsetLink().run():r.chain().focus().extendMarkRange("link").setLink({href:p}).run(),o(!1),q("")},className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700",children:"Add Link"})]})]})})]})}},22955:(a,b,c)=>{Promise.resolve().then(c.bind(c,33429)),Promise.resolve().then(c.bind(c,73441))},33429:(a,b,c)=>{"use strict";c.d(b,{default:()=>k});var d=c(60687),e=c(43210),f=c(16189),g=c(30474),h=c(10499),i=c(1104),j=c(46447);function k({achievement:a,isEdit:b=!1}){let c=(0,f.useRouter)(),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(!1),[o,p]=(0,e.useState)(""),[q,r]=(0,e.useState)({title:a?.title||"",description:a?.description||"",student_name:a?.student_name||"",student_class:a?.student_class||"",achievement_type:a?.achievement_type||"academic",achievement_date:a?.achievement_date?new Date(a.achievement_date).toISOString().slice(0,10):new Date().toISOString().slice(0,10),award_by:a?.award_by||"",position:a?.position||"",image_url:a?.image_url||"",certificate_url:a?.certificate_url||"",status:a?.status||(0,j.F9)(),is_published:a?.is_published===void 0||a.is_published}),s=async d=>{d.preventDefault(),l(!0),p("");try{let d=b?`/api/admin/achievements/${a?.id}`:"/api/admin/achievements",e=await fetch(d,{method:b?"PATCH":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(q)});if(e.ok)c.push("/admin/achievements");else{let a=await e.json();p(a.error||"Failed to save achievement")}}catch(a){p("An error occurred. Please try again.")}finally{l(!1)}};return(0,d.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,d.jsxs)("form",{onSubmit:s,className:"space-y-6 p-6",children:[o&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:o}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Achievement Image (Optional)"}),q.image_url&&(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(g.default,{src:q.image_url,alt:"Achievement image",width:200,height:150,className:"w-48 h-36 rounded object-cover"})}),(0,d.jsx)(h.A,{bucket:"achievements",folder:"images",validationType:"image",multiple:!1,onUploadStart:()=>n(!0),onUploadComplete:a=>{n(!1);let b=a.find(a=>a.success&&a.url);b&&r({...q,image_url:b.url})},className:"mb-4"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Certificate/Document (Optional)"}),q.certificate_url&&(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsxs)("a",{href:q.certificate_url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,d.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"View Certificate"]})}),(0,d.jsx)(h.A,{bucket:"achievements",folder:"certificates",validationType:"document",multiple:!1,onUploadStart:()=>n(!0),onUploadComplete:a=>{n(!1);let b=a.find(a=>a.success&&a.url);b&&r({...q,certificate_url:b.url})},className:"mb-4"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"Achievement Title *"}),(0,d.jsx)("input",{type:"text",id:"title",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:q.title,onChange:a=>r({...q,title:a.target.value}),placeholder:"Enter achievement title"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,d.jsx)(i.A,{content:q.description||"",onChange:a=>r({...q,description:a}),placeholder:"Describe the achievement in detail...",className:"min-h-[200px]"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"student_name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Student Name"}),(0,d.jsx)("input",{type:"text",id:"student_name",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:q.student_name,onChange:a=>r({...q,student_name:a.target.value}),placeholder:"Enter student name"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"student_class",className:"block text-sm font-medium text-gray-700 mb-2",children:"Class"}),(0,d.jsxs)("select",{id:"student_class",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:q.student_class,onChange:a=>r({...q,student_class:a.target.value}),children:[(0,d.jsx)("option",{value:"",children:"Select Class"}),["Class 6","Class 7","Class 8","Class 9","Class 10","Class 11","Class 12","Alumni"].map(a=>(0,d.jsx)("option",{value:a,children:a},a))]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"achievement_type",className:"block text-sm font-medium text-gray-700 mb-2",children:"Achievement Type *"}),(0,d.jsx)("select",{id:"achievement_type",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:q.achievement_type,onChange:a=>r({...q,achievement_type:a.target.value}),children:[{value:"academic",label:"Academic"},{value:"sports",label:"Sports"},{value:"cultural",label:"Cultural"},{value:"competition",label:"Competition"},{value:"scholarship",label:"Scholarship"},{value:"other",label:"Other"}].map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"achievement_date",className:"block text-sm font-medium text-gray-700 mb-2",children:"Achievement Date *"}),(0,d.jsx)("input",{type:"date",id:"achievement_date",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:q.achievement_date,onChange:a=>r({...q,achievement_date:a.target.value})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,d.jsxs)("select",{id:"status",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:q.status,onChange:a=>r({...q,status:a.target.value}),children:[(0,d.jsx)("option",{value:"new",children:"New"}),(0,d.jsx)("option",{value:"draft",children:"Draft"}),(0,d.jsx)("option",{value:"published",children:"Published"}),(0,d.jsx)("option",{value:"archived",children:"Archived"})]}),(0,d.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:(0,j.Ng)(q.status).description})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"award_by",className:"block text-sm font-medium text-gray-700 mb-2",children:"Awarded By"}),(0,d.jsx)("input",{type:"text",id:"award_by",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:q.award_by,onChange:a=>r({...q,award_by:a.target.value}),placeholder:"Organization or institution"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-2",children:"Position/Rank"}),(0,d.jsx)("input",{type:"text",id:"position",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:q.position,onChange:a=>r({...q,position:a.target.value}),placeholder:"e.g., 1st Place, AIR 100, Gold Medal"})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:"is_published",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:q.is_published,onChange:a=>r({...q,is_published:a.target.checked})}),(0,d.jsx)("label",{htmlFor:"is_published",className:"ml-2 block text-sm text-gray-700",children:"Publish achievement"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:()=>c.back(),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Cancel"}),(0,d.jsxs)("button",{type:"submit",disabled:k||m,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[m?"Uploading...":k?"Saving...":b?"Update":"Create"," Achievement"]})]})]})})}},46447:(a,b,c)=>{"use strict";c.d(b,{F9:()=>g,Ng:()=>e,qm:()=>f});let d={new:{label:"New",color:"text-blue-800",bgColor:"bg-blue-100",description:"Recently created content"},draft:{label:"Draft",color:"text-gray-800",bgColor:"bg-gray-100",description:"Work in progress, not published"},published:{label:"Published",color:"text-green-800",bgColor:"bg-green-100",description:"Live and visible to public"},archived:{label:"Archived",color:"text-yellow-800",bgColor:"bg-yellow-100",description:"Hidden from public, kept for reference"}};function e(a){return d[a]||d.draft}function f(a,b){let c=e(a),d=`${c.bgColor} ${c.color}`;return`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${d} ${b||""}`.trim()}function g(){return"new"}},60335:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AchievementForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AchievementForm.tsx","default")},62707:(a,b,c)=>{Promise.resolve().then(c.bind(c,60335)),Promise.resolve().then(c.bind(c,13779))}};