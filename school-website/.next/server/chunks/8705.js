exports.id=8705,exports.ids=[8705],exports.modules={1104:(a,b,c)=>{"use strict";c.d(b,{A:()=>m});var d=c(60687),e=c(7203),f=c(41123),g=c(79801),h=c(2188),i=c(84633),j=c(55023),k=c(36293),l=c(43210);function m({content:a,onChange:b,placeholder:c="Start writing...",className:m=""}){let[n,o]=(0,l.useState)(!1),[p,q]=(0,l.useState)(""),r=(0,e.hG)({extensions:[f.A,g.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),h.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-green-600 underline hover:text-green-700"}}),i.A.configure({types:["heading","paragraph"]}),j.A,k.xJ],content:a,onUpdate:({editor:a})=>{b(a.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}}});if(!r)return null;let s=({onClick:a,isActive:b=!1,disabled:c=!1,children:e,title:f})=>(0,d.jsx)("button",{type:"button",onClick:a,disabled:c,title:f,className:`p-2 rounded text-sm font-medium transition-colors ${b?"bg-green-100 text-green-700 border border-green-300":"text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent"} ${c?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:e});return(0,d.jsxs)("div",{className:`border border-gray-300 rounded-lg overflow-hidden ${m}`,children:[(0,d.jsxs)("div",{className:"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBold().run(),isActive:r.isActive("bold"),title:"Bold",children:(0,d.jsx)("strong",{children:"B"})}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleItalic().run(),isActive:r.isActive("italic"),title:"Italic",children:(0,d.jsx)("em",{children:"I"})}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleStrike().run(),isActive:r.isActive("strike"),title:"Strikethrough",children:(0,d.jsx)("s",{children:"S"})})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:1}).run(),isActive:r.isActive("heading",{level:1}),title:"Heading 1",children:"H1"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:2}).run(),isActive:r.isActive("heading",{level:2}),title:"Heading 2",children:"H2"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:3}).run(),isActive:r.isActive("heading",{level:3}),title:"Heading 3",children:"H3"})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBulletList().run(),isActive:r.isActive("bulletList"),title:"Bullet List",children:"•"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleOrderedList().run(),isActive:r.isActive("orderedList"),title:"Numbered List",children:"1."})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("left").run(),isActive:r.isActive({textAlign:"left"}),title:"Align Left",children:"⬅"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("center").run(),isActive:r.isActive({textAlign:"center"}),title:"Align Center",children:"↔"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("right").run(),isActive:r.isActive({textAlign:"right"}),title:"Align Right",children:"➡"})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>{q(r.getAttributes("link").href||""),o(!0)},isActive:r.isActive("link"),title:"Add Link",children:"\uD83D\uDD17"}),(0,d.jsx)(s,{onClick:()=>{let a=window.prompt("Enter image URL:");a&&r.chain().focus().setImage({src:a}).run()},title:"Add Image",children:"\uD83D\uDDBC"})]}),(0,d.jsxs)("div",{className:"flex gap-1",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBlockquote().run(),isActive:r.isActive("blockquote"),title:"Quote",children:'"'}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setHorizontalRule().run(),title:"Horizontal Rule",children:"―"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().undo().run(),disabled:!r.can().chain().focus().undo().run(),title:"Undo",children:"↶"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().redo().run(),disabled:!r.can().chain().focus().redo().run(),title:"Redo",children:"↷"})]})]}),(0,d.jsxs)("div",{className:"min-h-[200px] bg-white",children:[(0,d.jsx)(e.$Z,{editor:r,className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"}),r.isEmpty&&(0,d.jsx)("div",{className:"absolute top-16 left-4 text-gray-400 pointer-events-none",children:c})]}),n&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add Link"}),(0,d.jsx)("input",{type:"url",value:p,onChange:a=>q(a.target.value),placeholder:"Enter URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 mb-4",autoFocus:!0}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,d.jsx)("button",{type:"button",onClick:()=>o(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,d.jsx)("button",{type:"button",onClick:()=>{""===p?r.chain().focus().extendMarkRange("link").unsetLink().run():r.chain().focus().extendMarkRange("link").setLink({href:p}).run(),o(!1),q("")},className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700",children:"Add Link"})]})]})})]})}},22689:(a,b,c)=>{Promise.resolve().then(c.bind(c,13779)),Promise.resolve().then(c.bind(c,68709))},36835:(a,b,c)=>{"use strict";c.d(b,{default:()=>j});var d=c(60687),e=c(43210),f=c(16189),g=c(46447),h=c(10499),i=c(1104);function j({announcement:a,isEdit:b=!1}){let c=(0,f.useRouter)(),[j,k]=(0,e.useState)(!1),[l,m]=(0,e.useState)(""),[n,o]=(0,e.useState)({title:a?.title||"",content:a?.content||"",priority:a?.priority||"medium",status:a?.status||(0,g.F9)(),is_published:a?.is_published||!1,publish_date:a?.publish_date?new Date(a.publish_date).toISOString().slice(0,16):new Date().toISOString().slice(0,16),is_scheduled:!1,scheduled_publish_date:""}),[p,q]=(0,e.useState)([]),[r,s]=(0,e.useState)(!1),t=async d=>{d.preventDefault(),k(!0),m("");try{let d=b?`/api/admin/announcements/${a?.id}`:"/api/admin/announcements",e=await fetch(d,{method:b?"PATCH":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...n,publish_date:new Date(n.publish_date).toISOString(),attachments:p})});if(e.ok)c.push("/admin/announcements");else{let a=await e.json();m(a.error||"Failed to save announcement")}}catch(a){m("An error occurred. Please try again.")}finally{k(!1)}},u=async()=>{o({...n,is_published:!1});let a=document.getElementById("announcement-form");a&&a.requestSubmit()};return(0,d.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,d.jsxs)("form",{id:"announcement-form",onSubmit:t,className:"space-y-6 p-6",children:[l&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:l}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),(0,d.jsx)("input",{type:"text",id:"title",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:n.title,onChange:a=>o({...n,title:a.target.value}),placeholder:"Enter announcement title"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-2",children:"Content *"}),(0,d.jsx)(i.A,{content:n.content,onChange:a=>o({...n,content:a}),placeholder:"Enter announcement content...",className:"min-h-[300px]"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"priority",className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,d.jsxs)("select",{id:"priority",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:n.priority,onChange:a=>o({...n,priority:a.target.value}),children:[(0,d.jsx)("option",{value:"low",children:"Low"}),(0,d.jsx)("option",{value:"medium",children:"Medium"}),(0,d.jsx)("option",{value:"high",children:"High"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,d.jsxs)("select",{id:"status",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:n.status,onChange:a=>o({...n,status:a.target.value}),children:[(0,d.jsx)("option",{value:"new",children:"New"}),(0,d.jsx)("option",{value:"draft",children:"Draft"}),(0,d.jsx)("option",{value:"published",children:"Published"}),(0,d.jsx)("option",{value:"archived",children:"Archived"})]}),(0,d.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:(0,g.Ng)(n.status).description})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"publish_date",className:"block text-sm font-medium text-gray-700 mb-2",children:"Publish Date"}),(0,d.jsx)("input",{type:"datetime-local",id:"publish_date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:n.publish_date,onChange:a=>o({...n,publish_date:a.target.value})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Attachments (Optional)"}),(0,d.jsx)(h.A,{bucket:"announcements",folder:"attachments",validationType:"any",multiple:!0,maxFiles:5,onUploadStart:()=>s(!0),onUploadComplete:a=>{s(!1);let b=a.filter(a=>a.success&&a.url).map(a=>a.url);q(a=>[...a,...b])},className:"mb-4"}),p.length>0&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Attached Files:"}),p.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded",children:[(0,d.jsx)("span",{className:"text-sm text-gray-700 truncate",children:a.split("/").pop()}),(0,d.jsx)("button",{type:"button",onClick:()=>q(a=>a.filter((a,c)=>c!==b)),className:"text-red-600 hover:text-red-800 text-sm",children:"Remove"})]},b))]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:"is_published",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:n.is_published&&!n.is_scheduled,onChange:a=>o({...n,is_published:a.target.checked,is_scheduled:!1})}),(0,d.jsx)("label",{htmlFor:"is_published",className:"ml-2 block text-sm text-gray-700",children:"Publish immediately"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:"is_scheduled",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:n.is_scheduled,onChange:a=>o({...n,is_scheduled:a.target.checked,is_published:!1})}),(0,d.jsx)("label",{htmlFor:"is_scheduled",className:"ml-2 block text-sm text-gray-700",children:"Schedule for later"})]}),n.is_scheduled&&(0,d.jsxs)("div",{className:"ml-6",children:[(0,d.jsx)("label",{htmlFor:"scheduled_publish_date",className:"block text-sm font-medium text-gray-700 mb-2",children:"Scheduled Publish Date & Time"}),(0,d.jsx)("input",{type:"datetime-local",id:"scheduled_publish_date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:n.scheduled_publish_date,onChange:a=>o({...n,scheduled_publish_date:a.target.value}),min:new Date().toISOString().slice(0,16)}),(0,d.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"The announcement will be automatically published at the scheduled time"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:()=>c.back(),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Cancel"}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[!b&&(0,d.jsx)("button",{type:"button",onClick:u,disabled:j,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:"Save as Draft"}),(0,d.jsxs)("button",{type:"submit",disabled:j||r,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[r?"Uploading...":j?"Saving...":b?"Update":"Create"," Announcement"]})]})]})]})})}},46447:(a,b,c)=>{"use strict";c.d(b,{F9:()=>g,Ng:()=>e,qm:()=>f});let d={new:{label:"New",color:"text-blue-800",bgColor:"bg-blue-100",description:"Recently created content"},draft:{label:"Draft",color:"text-gray-800",bgColor:"bg-gray-100",description:"Work in progress, not published"},published:{label:"Published",color:"text-green-800",bgColor:"bg-green-100",description:"Live and visible to public"},archived:{label:"Archived",color:"text-yellow-800",bgColor:"bg-yellow-100",description:"Hidden from public, kept for reference"}};function e(a){return d[a]||d.draft}function f(a,b){let c=e(a),d=`${c.bgColor} ${c.color}`;return`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${d} ${b||""}`.trim()}function g(){return"new"}},59137:(a,b,c)=>{Promise.resolve().then(c.bind(c,73441)),Promise.resolve().then(c.bind(c,36835))},68709:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AnnouncementForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AnnouncementForm.tsx","default")}};