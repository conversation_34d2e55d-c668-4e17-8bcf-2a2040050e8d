exports.id=1195,exports.ids=[1195],exports.modules={10455:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f);function h({placeholder:a="Search announcements, faculty, achievements...",className:b="",showFilters:c=!0}){let[f,h]=(0,e.useState)(""),[i,j]=(0,e.useState)([]),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(!1),[o,p]=(0,e.useState)("all"),[q,r]=(0,e.useState)(0),s=(0,e.useRef)(null),t=(0,e.useRef)(null);(0,e.useCallback)(async()=>{l(!0);try{let a=await fetch(`/api/search?q=${encodeURIComponent(f)}&type=${o}&limit=10`),b=await a.json();a.ok&&(j(b.results||[]),r(b.total||0),n(!0))}catch(a){console.error("Search error:",a)}finally{l(!1)}},[f,o]);let u=()=>{n(!1),h("")};return(0,d.jsxs)("div",{ref:s,className:`relative ${b}`,children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,d.jsx)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,d.jsx)("input",{ref:t,type:"text",value:f,onChange:a=>h(a.target.value),onFocus:()=>f.length>=2&&n(!0),placeholder:a,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"}),k&&(0,d.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,d.jsxs)("svg",{className:"animate-spin h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})]}),c&&(0,d.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:[{value:"all",label:"All Content"},{value:"announcements",label:"Announcements"},{value:"faculty",label:"Faculty"},{value:"achievements",label:"Achievements"}].map(a=>(0,d.jsx)("button",{onClick:()=>p(a.value),className:`px-3 py-1 text-sm rounded-full transition-colors ${o===a.value?"bg-green-100 text-green-700 border border-green-300":"bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300"}`,children:a.label},a.value))}),m&&(0,d.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto",children:i.length>0?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"px-4 py-2 border-b border-gray-100 text-sm text-gray-600",children:[q," result",1!==q?"s":""," found"]}),i.map(a=>(0,d.jsx)(g(),{href:a.url,onClick:u,className:"block px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("span",{className:"text-lg",children:(a=>{switch(a){case"announcement":return"\uD83D\uDCE2";case"faculty":return"\uD83D\uDC68‍\uD83C\uDFEB";case"achievement":return"\uD83C\uDFC6";default:return"\uD83D\uDCC4"}})(a.type)}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:a.title}),(0,d.jsx)("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${(a=>{switch(a){case"announcement":return"bg-blue-100 text-blue-800";case"faculty":return"bg-green-100 text-green-800";case"achievement":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(a.type)}`,children:a.type})]}),a.content&&(0,d.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2 mb-1",children:a.content}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[a.student&&(0,d.jsxs)("span",{children:["Student: ",a.student]}),a.studentClass&&(0,d.jsxs)("span",{children:["Class: ",a.studentClass]}),a.achievementType&&(0,d.jsxs)("span",{children:["Type: ",a.achievementType]}),a.date&&(0,d.jsxs)("span",{children:["Date: ",new Date(a.date).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})]})]})]})]})},`${a.type}-${a.id}`)),q>i.length&&(0,d.jsxs)("div",{className:"px-4 py-2 text-center text-sm text-gray-500 border-t border-gray-100",children:["Showing ",i.length," of ",q," results"]})]}):f.length>=2&&!k?(0,d.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,d.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,d.jsxs)("p",{children:['No results found for "',f,'"']}),(0,d.jsx)("p",{className:"text-sm mt-1",children:"Try different keywords or check spelling"})]}):f.length<2?(0,d.jsx)("div",{className:"px-4 py-6 text-center text-gray-500",children:(0,d.jsx)("p",{children:"Type at least 2 characters to search"})}):null})]})}},21891:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(60687),e=c(85814),f=c.n(e),g=c(43210),h=c(10455);function i(){let[a,b]=(0,g.useState)(!1);return(0,d.jsx)("header",{className:"bg-white shadow-sm",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)("div",{className:"flex-shrink-0",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-green-900",children:"Meritorious School Ludhiana"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Government of Punjab"})]})}),(0,d.jsx)("nav",{className:"hidden md:block",children:(0,d.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,d.jsx)(f(),{href:"/",className:"text-green-900 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Home"}),(0,d.jsx)(f(),{href:"/about",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"About"}),(0,d.jsx)(f(),{href:"/academics",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Academics"}),(0,d.jsx)(f(),{href:"/gallery",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Gallery"}),(0,d.jsx)(f(),{href:"/faculty",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Faculty"}),(0,d.jsx)(f(),{href:"/admissions",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Admissions"}),(0,d.jsx)(f(),{href:"/contact",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Contact"})]})}),(0,d.jsx)("div",{className:"hidden md:block ml-6",children:(0,d.jsx)(h.default,{placeholder:"Search...",className:"w-64",showFilters:!1})}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)("button",{onClick:()=>b(!a),className:"text-gray-600 hover:text-green-700 focus:outline-none focus:text-green-700",children:(0,d.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a?(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),a&&(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 rounded-lg mb-4",children:[(0,d.jsx)(f(),{href:"/",className:"text-green-900 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>b(!1),children:"Home"}),(0,d.jsx)(f(),{href:"/about",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>b(!1),children:"About"}),(0,d.jsx)(f(),{href:"/academics",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>b(!1),children:"Academics"}),(0,d.jsx)(f(),{href:"/gallery",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>b(!1),children:"Gallery"}),(0,d.jsx)(f(),{href:"/faculty",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>b(!1),children:"Faculty"}),(0,d.jsx)(f(),{href:"/admissions",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>b(!1),children:"Admissions"}),(0,d.jsx)(f(),{href:"/contact",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>b(!1),children:"Contact"})]})})]})})}},25488:()=>{},35216:()=>{},51176:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},61135:()=>{},64328:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},76926:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(61120));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(2202),f=c.n(e),g=c(64988),h=c.n(g);c(61135);let i={title:"Senior Secondary Residential School for Meritorious Students - Ludhiana",description:"Government of Punjab initiative providing free education, coaching, food, and accommodation to meritorious students from economically backward families. Established 2014 in Civil Lines, Ludhiana.",keywords:"meritorious school, ludhiana, government of punjab, free education, residential school, JEE coaching, NEET coaching, admission 2024",authors:[{name:"Government of Punjab"}],openGraph:{title:"Senior Secondary Residential School for Meritorious Students",description:"Free education and residential facilities for meritorious students in Punjab",type:"website",locale:"en_IN"}};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}}};