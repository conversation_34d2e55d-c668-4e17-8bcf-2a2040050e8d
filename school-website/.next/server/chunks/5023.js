exports.id=5023,exports.ids=[5023],exports.modules={1104:(a,b,c)=>{"use strict";c.d(b,{A:()=>m});var d=c(60687),e=c(7203),f=c(41123),g=c(79801),h=c(2188),i=c(84633),j=c(55023),k=c(36293),l=c(43210);function m({content:a,onChange:b,placeholder:c="Start writing...",className:m=""}){let[n,o]=(0,l.useState)(!1),[p,q]=(0,l.useState)(""),r=(0,e.hG)({extensions:[f.A,g.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),h.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-green-600 underline hover:text-green-700"}}),i.A.configure({types:["heading","paragraph"]}),j.A,k.xJ],content:a,onUpdate:({editor:a})=>{b(a.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}}});if(!r)return null;let s=({onClick:a,isActive:b=!1,disabled:c=!1,children:e,title:f})=>(0,d.jsx)("button",{type:"button",onClick:a,disabled:c,title:f,className:`p-2 rounded text-sm font-medium transition-colors ${b?"bg-green-100 text-green-700 border border-green-300":"text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent"} ${c?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:e});return(0,d.jsxs)("div",{className:`border border-gray-300 rounded-lg overflow-hidden ${m}`,children:[(0,d.jsxs)("div",{className:"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBold().run(),isActive:r.isActive("bold"),title:"Bold",children:(0,d.jsx)("strong",{children:"B"})}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleItalic().run(),isActive:r.isActive("italic"),title:"Italic",children:(0,d.jsx)("em",{children:"I"})}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleStrike().run(),isActive:r.isActive("strike"),title:"Strikethrough",children:(0,d.jsx)("s",{children:"S"})})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:1}).run(),isActive:r.isActive("heading",{level:1}),title:"Heading 1",children:"H1"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:2}).run(),isActive:r.isActive("heading",{level:2}),title:"Heading 2",children:"H2"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleHeading({level:3}).run(),isActive:r.isActive("heading",{level:3}),title:"Heading 3",children:"H3"})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBulletList().run(),isActive:r.isActive("bulletList"),title:"Bullet List",children:"•"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleOrderedList().run(),isActive:r.isActive("orderedList"),title:"Numbered List",children:"1."})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("left").run(),isActive:r.isActive({textAlign:"left"}),title:"Align Left",children:"⬅"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("center").run(),isActive:r.isActive({textAlign:"center"}),title:"Align Center",children:"↔"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setTextAlign("right").run(),isActive:r.isActive({textAlign:"right"}),title:"Align Right",children:"➡"})]}),(0,d.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,d.jsx)(s,{onClick:()=>{q(r.getAttributes("link").href||""),o(!0)},isActive:r.isActive("link"),title:"Add Link",children:"\uD83D\uDD17"}),(0,d.jsx)(s,{onClick:()=>{let a=window.prompt("Enter image URL:");a&&r.chain().focus().setImage({src:a}).run()},title:"Add Image",children:"\uD83D\uDDBC"})]}),(0,d.jsxs)("div",{className:"flex gap-1",children:[(0,d.jsx)(s,{onClick:()=>r.chain().focus().toggleBlockquote().run(),isActive:r.isActive("blockquote"),title:"Quote",children:'"'}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().setHorizontalRule().run(),title:"Horizontal Rule",children:"―"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().undo().run(),disabled:!r.can().chain().focus().undo().run(),title:"Undo",children:"↶"}),(0,d.jsx)(s,{onClick:()=>r.chain().focus().redo().run(),disabled:!r.can().chain().focus().redo().run(),title:"Redo",children:"↷"})]})]}),(0,d.jsxs)("div",{className:"min-h-[200px] bg-white",children:[(0,d.jsx)(e.$Z,{editor:r,className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"}),r.isEmpty&&(0,d.jsx)("div",{className:"absolute top-16 left-4 text-gray-400 pointer-events-none",children:c})]}),n&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add Link"}),(0,d.jsx)("input",{type:"url",value:p,onChange:a=>q(a.target.value),placeholder:"Enter URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 mb-4",autoFocus:!0}),(0,d.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,d.jsx)("button",{type:"button",onClick:()=>o(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,d.jsx)("button",{type:"button",onClick:()=>{""===p?r.chain().focus().extendMarkRange("link").unsetLink().run():r.chain().focus().extendMarkRange("link").setLink({href:p}).run(),o(!1),q("")},className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700",children:"Add Link"})]})]})})]})}},27468:(a,b,c)=>{"use strict";c.d(b,{default:()=>j});var d=c(60687),e=c(43210),f=c(16189),g=c(30474),h=c(10499),i=c(1104);function j({faculty:a,isEdit:b=!1}){let c=(0,f.useRouter)(),[j,k]=(0,e.useState)(!1),[l,m]=(0,e.useState)(!1),[n,o]=(0,e.useState)(""),[p,q]=(0,e.useState)({name:a?.name||"",designation:a?.designation||"",department:a?.department||"",qualification:a?.qualification||"",experience:a?.experience||"",specialization:a?.specialization||"",email:a?.email||"",phone:a?.phone||"",image_url:a?.image_url||"",bio:a?.bio||"",is_published:a?.is_published===void 0||a.is_published}),r=async d=>{d.preventDefault(),k(!0),o("");try{let d=b?`/api/admin/faculty/${a?.id}`:"/api/admin/faculty",e=await fetch(d,{method:b?"PATCH":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)});if(e.ok)c.push("/admin/faculty");else{let a=await e.json();o(a.error||"Failed to save faculty member")}}catch(a){o("An error occurred. Please try again.")}finally{k(!1)}};return(0,d.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,d.jsxs)("form",{onSubmit:r,className:"space-y-6 p-6",children:[n&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:n}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Profile Image"}),p.image_url&&(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(g.default,{src:p.image_url,alt:"Current profile",width:128,height:128,className:"w-32 h-32 rounded-full object-cover"})}),(0,d.jsx)(h.A,{bucket:"faculty",folder:"profiles",validationType:"image",multiple:!1,onUploadStart:()=>m(!0),onUploadComplete:a=>{m(!1);let b=a.find(a=>a.success&&a.url);b&&q({...p,image_url:b.url})},className:"mb-4"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,d.jsx)("input",{type:"text",id:"name",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.name,onChange:a=>q({...p,name:a.target.value}),placeholder:"Enter full name"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"designation",className:"block text-sm font-medium text-gray-700 mb-2",children:"Designation *"}),(0,d.jsx)("input",{type:"text",id:"designation",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.designation,onChange:a=>q({...p,designation:a.target.value}),placeholder:"e.g., Principal, Teacher, HOD"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"department",className:"block text-sm font-medium text-gray-700 mb-2",children:"Department"}),(0,d.jsxs)("select",{id:"department",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.department,onChange:a=>q({...p,department:a.target.value}),children:[(0,d.jsx)("option",{value:"",children:"Select Department"}),["Mathematics","Science","English","Hindi","Social Studies","Computer Science","Physical Education","Arts","Commerce","Administration"].map(a=>(0,d.jsx)("option",{value:a,children:a},a))]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"qualification",className:"block text-sm font-medium text-gray-700 mb-2",children:"Qualification"}),(0,d.jsx)("input",{type:"text",id:"qualification",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.qualification,onChange:a=>q({...p,qualification:a.target.value}),placeholder:"e.g., M.A., B.Ed., Ph.D."})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"experience",className:"block text-sm font-medium text-gray-700 mb-2",children:"Experience"}),(0,d.jsx)("input",{type:"text",id:"experience",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.experience,onChange:a=>q({...p,experience:a.target.value}),placeholder:"e.g., 10 years"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"specialization",className:"block text-sm font-medium text-gray-700 mb-2",children:"Specialization"}),(0,d.jsx)("input",{type:"text",id:"specialization",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.specialization,onChange:a=>q({...p,specialization:a.target.value}),placeholder:"Area of expertise"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,d.jsx)("input",{type:"email",id:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.email,onChange:a=>q({...p,email:a.target.value}),placeholder:"<EMAIL>"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone"}),(0,d.jsx)("input",{type:"tel",id:"phone",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.phone,onChange:a=>q({...p,phone:a.target.value}),placeholder:"+91 9876543210"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-2",children:"Biography"}),(0,d.jsx)(i.A,{content:p.bio||"",onChange:a=>q({...p,bio:a}),placeholder:"Brief biography and achievements...",className:"min-h-[200px]"})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",id:"is_published",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:p.is_published,onChange:a=>q({...p,is_published:a.target.checked})}),(0,d.jsx)("label",{htmlFor:"is_published",className:"ml-2 block text-sm text-gray-700",children:"Publish profile"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,d.jsx)("button",{type:"button",onClick:()=>c.back(),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Cancel"}),(0,d.jsxs)("button",{type:"submit",disabled:j||l,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[l?"Uploading...":j?"Saving...":b?"Update":"Create"," Faculty"]})]})]})})}},31036:(a,b,c)=>{Promise.resolve().then(c.bind(c,73441)),Promise.resolve().then(c.bind(c,27468))},72892:(a,b,c)=>{Promise.resolve().then(c.bind(c,13779)),Promise.resolve().then(c.bind(c,80570))},80570:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/FacultyForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/FacultyForm.tsx","default")}};