{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "dynamicRoutes": [{"page": "/admin/achievements/[id]/edit", "regex": "^/admin/achievements/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/achievements/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/admin/announcements/[id]/edit", "regex": "^/admin/announcements/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/announcements/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/admin/faculty/[id]/edit", "regex": "^/admin/faculty/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/faculty/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/api/admin/achievements/[id]", "regex": "^/api/admin/achievements/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/achievements/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/announcements/[id]", "regex": "^/api/admin/announcements/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/announcements/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/contacts/[id]", "regex": "^/api/admin/contacts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/contacts/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/faculty/[id]", "regex": "^/api/admin/faculty/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/faculty/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/gallery/[id]", "regex": "^/api/admin/gallery/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/gallery/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/academics", "regex": "^/academics(?:/)?$", "routeKeys": {}, "namedRegex": "^/academics(?:/)?$"}, {"page": "/admin/achievements", "regex": "^/admin/achievements(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/achievements(?:/)?$"}, {"page": "/admin/achievements/new", "regex": "^/admin/achievements/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/achievements/new(?:/)?$"}, {"page": "/admin/announcements", "regex": "^/admin/announcements(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/announcements(?:/)?$"}, {"page": "/admin/announcements/new", "regex": "^/admin/announcements/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/announcements/new(?:/)?$"}, {"page": "/admin/audit", "regex": "^/admin/audit(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/audit(?:/)?$"}, {"page": "/admin/backup", "regex": "^/admin/backup(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/backup(?:/)?$"}, {"page": "/admin/content", "regex": "^/admin/content(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/content(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/admin/faculty", "regex": "^/admin/faculty(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/faculty(?:/)?$"}, {"page": "/admin/faculty/new", "regex": "^/admin/faculty/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/faculty/new(?:/)?$"}, {"page": "/admin/gallery", "regex": "^/admin/gallery(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/gallery(?:/)?$"}, {"page": "/admin/gallery/upload", "regex": "^/admin/gallery/upload(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/gallery/upload(?:/)?$"}, {"page": "/admin/login", "regex": "^/admin/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/login(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/setup", "regex": "^/admin/setup(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/setup(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/admissions", "regex": "^/admissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admissions(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/faculty", "regex": "^/faculty(?:/)?$", "routeKeys": {}, "namedRegex": "^/faculty(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/gallery", "regex": "^/gallery(?:/)?$", "routeKeys": {}, "namedRegex": "^/gallery(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}}