"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3893],{603:(e,s,n)=>{n.d(s,{A:()=>h});var a=n(5155),t=n(2115),r=n(5647),l=n(9509);let o=(0,r.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co",l.env.SUPABASE_SERVICE_ROLE_KEY),i={image:{maxSize:5242880,allowedTypes:["image/jpeg","image/png","image/webp","image/gif"],allowedExtensions:[".jpg",".jpeg",".png",".webp",".gif"]},document:{maxSize:0xa00000,allowedTypes:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],allowedExtensions:[".pdf",".doc",".docx"]},any:{maxSize:0xa00000,allowedTypes:["image/jpeg","image/png","image/webp","image/gif","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],allowedExtensions:[".jpg",".jpeg",".png",".webp",".gif",".pdf",".doc",".docx"]}};function c(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"any",n=[],a=i[s];e.size>a.maxSize&&n.push("File size must be less than ".concat(m(a.maxSize))),a.allowedTypes.includes(e.type)||n.push("File type ".concat(e.type," is not allowed. Allowed types: ").concat(a.allowedTypes.join(", ")));let t=d(e.name);return a.allowedExtensions.includes(t)||n.push("File extension ".concat(t," is not allowed. Allowed extensions: ").concat(a.allowedExtensions.join(", "))),n}function d(e){return e.toLowerCase().substring(e.lastIndexOf("."))}function m(e){if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]}async function x(e,s){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"any";try{let t=c(e,a);if(t.length>0)return{success:!1,error:t.join(", ")};let r=function(e){let s=d(e),n=Date.now(),a=Math.random().toString(36).substring(2,8),t=e.replace(s,"").replace(/[^a-zA-Z0-9]/g,"_");return"".concat(t,"_").concat(n,"_").concat(a).concat(s)}(e.name),l=n?"".concat(n,"/").concat(r):r,{error:i}=await o.storage.from(s).upload(l,e,{cacheControl:"3600",upsert:!1});if(i)return{success:!1,error:i.message};let{data:m}=o.storage.from(s).getPublicUrl(l);return{success:!0,url:m.publicUrl,path:l}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Upload failed"}}}function h(e){let{bucket:s,folder:n="",validationType:r="any",multiple:l=!1,onUploadComplete:o,onUploadStart:d,className:h="",accept:u,maxFiles:p=10}=e,[g,f]=(0,t.useState)([]),[j,v]=(0,t.useState)(!1),y=(0,t.useRef)(null),k=i[r],w=(0,t.useCallback)(async e=>{if(!e||0===e.length)return;let a=Array.from(e);if(a.length>p)return void alert("Maximum ".concat(p," files allowed"));let t=[],l=[];if(a.forEach(e=>{let s=c(e,r);s.length>0?l.push("".concat(e.name,": ").concat(s.join(", "))):t.push(e)}),l.length>0)return void alert("Validation errors:\n".concat(l.join("\n")));if(0===t.length)return;f(t.map(e=>({file:e,progress:0,status:"uploading"}))),null==d||d();let i=[];for(let e=0;e<t.length;e++){let a=t[e];try{let t=await x(a,s,n,r);i.push(t),f(s=>s.map((s,n)=>n===e?{...s,progress:100,status:t.success?"success":"error",result:t}:s))}catch(n){let s={success:!1,error:n instanceof Error?n.message:"Upload failed"};i.push(s),f(n=>n.map((n,a)=>a===e?{...n,progress:100,status:"error",result:s}:n))}}null==o||o(i),setTimeout(()=>{f([])},3e3)},[s,n,r,p,o,d]),N=(0,t.useCallback)(e=>{e.preventDefault(),v(!0)},[]),b=(0,t.useCallback)(e=>{e.preventDefault(),v(!1)},[]),L=(0,t.useCallback)(e=>{e.preventDefault(),v(!1),w(e.dataTransfer.files)},[w]),M=(0,t.useCallback)(e=>{w(e.target.files),e.target.value=""},[w]),C=(0,t.useCallback)(()=>{var e;null==(e=y.current)||e.click()},[]);return(0,a.jsxs)("div",{className:"space-y-4 ".concat(h),children:[(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ".concat(j?"border-green-500 bg-green-50":"border-gray-300 hover:border-green-400 hover:bg-gray-50"),onDragOver:N,onDragLeave:b,onDrop:L,onClick:C,children:[(0,a.jsx)("input",{ref:y,type:"file",multiple:l,accept:u||k.allowedTypes.join(","),onChange:M,className:"hidden"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:(0,a.jsx)("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"font-medium text-green-600",children:"Click to upload"})," or drag and drop"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[k.allowedExtensions.join(", ")," up to ",m(k.maxSize)]}),l&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Maximum ",p," files"]})]})]}),g.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Uploading Files"}),g.map((e,s)=>{var n;return(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900 truncate",children:e.file.name}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:m(e.file.size)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat("success"===e.status?"bg-green-500":"error"===e.status?"bg-red-500":"bg-blue-500"),style:{width:"".concat(e.progress,"%")}})}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:["uploading"===e.status&&(0,a.jsxs)("svg",{className:"animate-spin h-4 w-4 text-blue-500",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"success"===e.status&&(0,a.jsx)("svg",{className:"h-4 w-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"error"===e.status&&(0,a.jsx)("svg",{className:"h-4 w-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})]}),(null==(n=e.result)?void 0:n.error)&&(0,a.jsx)("div",{className:"mt-2 text-xs text-red-600",children:e.result.error})]},s)})]})]})}},6071:(e,s,n)=>{n.d(s,{default:()=>i});var a=n(5155),t=n(2115),r=n(6874),l=n.n(r),o=n(5695);function i(e){let{children:s,currentAdmin:n}=e,[r,i]=(0,t.useState)(!1),c=(0,o.useRouter)(),d=async()=>{try{await fetch("/api/admin/auth/logout",{method:"POST"}),c.push("/admin/login")}catch(e){console.error("Logout error:",e)}},m=[{name:"Dashboard",href:"/admin/dashboard",icon:"dashboard"},{name:"Announcements",href:"/admin/announcements",icon:"announcements"},{name:"Faculty",href:"/admin/faculty",icon:"faculty"},{name:"Gallery",href:"/admin/gallery",icon:"gallery"},{name:"Achievements",href:"/admin/achievements",icon:"trophy"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Users",href:"/admin/users",icon:"users",adminOnly:!0},{name:"Audit Logs",href:"/admin/audit",icon:"audit",adminOnly:!0},{name:"Backup",href:"/admin/backup",icon:"backup",adminOnly:!0},{name:"Settings",href:"/admin/settings",icon:"settings"},{name:"Achievements",href:"/admin/achievements",icon:"achievements"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Settings",href:"/admin/settings",icon:"settings"}],x=e=>{let s={dashboard:(0,a.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"})]}),announcements:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"})}),faculty:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),gallery:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),achievements:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),content:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),settings:(0,a.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})};return s[e]||s.dashboard};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsxs)("div",{className:"fixed inset-0 flex z-40 md:hidden ".concat(r?"":"hidden"),children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>i(!1)}),(0,a.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,a.jsx)("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>i(!1),children:(0,a.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})}),(0,a.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,a.jsx)("h1",{className:"text-lg font-semibold text-green-600",children:"Admin Panel"})}),(0,a.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:m.filter(e=>!e.adminOnly||"super_admin"===n.role).map(e=>(0,a.jsxs)(l(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium rounded-md",children:[x(e.icon),(0,a.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})]})]}),(0,a.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,a.jsx)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:(0,a.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,a.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,a.jsx)("h1",{className:"text-xl font-bold text-green-600",children:"Admin Panel"})}),(0,a.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:m.filter(e=>!e.adminOnly||"super_admin"===n.role).map(e=>(0,a.jsxs)(l(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:[x(e.icon),(0,a.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})})}),(0,a.jsxs)("div",{className:"md:pl-64 flex flex-col flex-1",children:[(0,a.jsx)("div",{className:"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100",children:(0,a.jsx)("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500",onClick:()=>i(!0),children:(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,a.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Meritorious School Admin"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(l(),{href:"/",className:"text-gray-500 hover:text-gray-700 text-sm",target:"_blank",children:"View Website"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-700",children:n.name}),(0,a.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:n.role}),(0,a.jsx)("button",{onClick:d,className:"text-gray-500 hover:text-gray-700 text-sm",children:"Logout"})]})]})]})})}),(0,a.jsx)("main",{className:"flex-1",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:s})})]})]})}}}]);