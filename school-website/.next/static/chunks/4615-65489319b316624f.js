"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4615],{4615:(e,t,s)=>{s.d(t,{default:()=>o});var r=s(5155),a=s(6874),n=s.n(a),l=s(2115),c=s(8337);function o(){let[e,t]=(0,l.useState)(!1);return(0,r.jsx)("header",{className:"bg-white shadow-sm",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("div",{className:"flex-shrink-0",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-green-900",children:"Meritorious School Ludhiana"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Government of Punjab"})]})}),(0,r.jsx)("nav",{className:"hidden md:block",children:(0,r.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,r.jsx)(n(),{href:"/",className:"text-green-900 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Home"}),(0,r.jsx)(n(),{href:"/about",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"About"}),(0,r.jsx)(n(),{href:"/academics",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Academics"}),(0,r.jsx)(n(),{href:"/gallery",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Gallery"}),(0,r.jsx)(n(),{href:"/faculty",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Faculty"}),(0,r.jsx)(n(),{href:"/admissions",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Admissions"}),(0,r.jsx)(n(),{href:"/contact",className:"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium",children:"Contact"})]})}),(0,r.jsx)("div",{className:"hidden md:block ml-6",children:(0,r.jsx)(c.default,{placeholder:"Search...",className:"w-64",showFilters:!1})}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)("button",{onClick:()=>t(!e),className:"text-gray-600 hover:text-green-700 focus:outline-none focus:text-green-700",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 rounded-lg mb-4",children:[(0,r.jsx)(n(),{href:"/",className:"text-green-900 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Home"}),(0,r.jsx)(n(),{href:"/about",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"About"}),(0,r.jsx)(n(),{href:"/academics",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Academics"}),(0,r.jsx)(n(),{href:"/gallery",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Gallery"}),(0,r.jsx)(n(),{href:"/faculty",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Faculty"}),(0,r.jsx)(n(),{href:"/admissions",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Admissions"}),(0,r.jsx)(n(),{href:"/contact",className:"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>t(!1),children:"Contact"})]})})]})})}},8337:(e,t,s)=>{s.d(t,{default:()=>c});var r=s(5155),a=s(2115),n=s(6874),l=s.n(n);function c(e){let{placeholder:t="Search announcements, faculty, achievements...",className:s="",showFilters:n=!0}=e,[c,o]=(0,a.useState)(""),[d,i]=(0,a.useState)([]),[x,m]=(0,a.useState)(!1),[h,u]=(0,a.useState)(!1),[p,g]=(0,a.useState)("all"),[y,f]=(0,a.useState)(0),b=(0,a.useRef)(null),j=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=e=>{b.current&&!b.current.contains(e.target)&&u(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let v=(0,a.useCallback)(async()=>{m(!0);try{let e=await fetch("/api/search?q=".concat(encodeURIComponent(c),"&type=").concat(p,"&limit=10")),t=await e.json();e.ok&&(i(t.results||[]),f(t.total||0),u(!0))}catch(e){console.error("Search error:",e)}finally{m(!1)}},[c,p]);(0,a.useEffect)(()=>{let e=setTimeout(()=>{c.trim().length>=2?v():(i([]),u(!1))},300);return()=>clearTimeout(e)},[c,p,v]);let N=()=>{u(!1),o("")};return(0,r.jsxs)("div",{ref:b,className:"relative ".concat(s),children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,r.jsx)("input",{ref:j,type:"text",value:c,onChange:e=>o(e.target.value),onFocus:()=>c.length>=2&&u(!0),placeholder:t,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"}),x&&(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,r.jsxs)("svg",{className:"animate-spin h-5 w-5 text-gray-400",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})]}),n&&(0,r.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:[{value:"all",label:"All Content"},{value:"announcements",label:"Announcements"},{value:"faculty",label:"Faculty"},{value:"achievements",label:"Achievements"}].map(e=>(0,r.jsx)("button",{onClick:()=>g(e.value),className:"px-3 py-1 text-sm rounded-full transition-colors ".concat(p===e.value?"bg-green-100 text-green-700 border border-green-300":"bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300"),children:e.label},e.value))}),h&&(0,r.jsx)("div",{className:"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto",children:d.length>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-gray-100 text-sm text-gray-600",children:[y," result",1!==y?"s":""," found"]}),d.map(e=>(0,r.jsx)(l(),{href:e.url,onClick:N,className:"block px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"announcement":return"\uD83D\uDCE2";case"faculty":return"\uD83D\uDC68‍\uD83C\uDFEB";case"achievement":return"\uD83C\uDFC6";default:return"\uD83D\uDCC4"}})(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"announcement":return"bg-blue-100 text-blue-800";case"faculty":return"bg-green-100 text-green-800";case"achievement":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(e.type)),children:e.type})]}),e.content&&(0,r.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2 mb-1",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[e.student&&(0,r.jsxs)("span",{children:["Student: ",e.student]}),e.studentClass&&(0,r.jsxs)("span",{children:["Class: ",e.studentClass]}),e.achievementType&&(0,r.jsxs)("span",{children:["Type: ",e.achievementType]}),e.date&&(0,r.jsxs)("span",{children:["Date: ",new Date(e.date).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})]})]})]})]})},"".concat(e.type,"-").concat(e.id))),y>d.length&&(0,r.jsxs)("div",{className:"px-4 py-2 text-center text-sm text-gray-500 border-t border-gray-100",children:["Showing ",d.length," of ",y," results"]})]}):c.length>=2&&!x?(0,r.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,r.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,r.jsxs)("p",{children:['No results found for "',c,'"']}),(0,r.jsx)("p",{className:"text-sm mt-1",children:"Try different keywords or check spelling"})]}):c.length<2?(0,r.jsx)("div",{className:"px-4 py-6 text-center text-gray-500",children:(0,r.jsx)("p",{children:"Type at least 2 characters to search"})}):null})]})}}}]);