{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/SearchComponent.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useRef } from 'react';\nimport Link from 'next/link';\n\ninterface SearchResult {\n  id: string;\n  type: 'announcement' | 'faculty' | 'achievement';\n  title: string;\n  content: string;\n  subtitle?: string;\n  student?: string;\n  studentClass?: string;\n  achievementType?: string;\n  date?: string;\n  url: string;\n  relevance: number;\n}\n\ninterface SearchComponentProps {\n  placeholder?: string;\n  className?: string;\n  showFilters?: boolean;\n}\n\nexport default function SearchComponent({ \n  placeholder = \"Search announcements, faculty, achievements...\",\n  className = \"\",\n  showFilters = true\n}: SearchComponentProps) {\n  const [query, setQuery] = useState('');\n  const [results, setResults] = useState<SearchResult[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [showResults, setShowResults] = useState(false);\n  const [selectedType, setSelectedType] = useState('all');\n  const [total, setTotal] = useState(0);\n  \n  const searchRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const searchTypes = [\n    { value: 'all', label: 'All Content' },\n    { value: 'announcements', label: 'Announcements' },\n    { value: 'faculty', label: 'Faculty' },\n    { value: 'achievements', label: 'Achievements' },\n  ];\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setShowResults(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  useEffect(() => {\n    const delayedSearch = setTimeout(() => {\n      if (query.trim().length >= 2) {\n        performSearch();\n      } else {\n        setResults([]);\n        setShowResults(false);\n      }\n    }, 300);\n\n    return () => clearTimeout(delayedSearch);\n  }, [query, selectedType]);\n\n  const performSearch = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(\n        `/api/search?q=${encodeURIComponent(query)}&type=${selectedType}&limit=10`\n      );\n      const data = await response.json();\n      \n      if (response.ok) {\n        setResults(data.results || []);\n        setTotal(data.total || 0);\n        setShowResults(true);\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleResultClick = () => {\n    setShowResults(false);\n    setQuery('');\n  };\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'announcement':\n        return '📢';\n      case 'faculty':\n        return '👨‍🏫';\n      case 'achievement':\n        return '🏆';\n      default:\n        return '📄';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'announcement':\n        return 'bg-blue-100 text-blue-800';\n      case 'faculty':\n        return 'bg-green-100 text-green-800';\n      case 'achievement':\n        return 'bg-yellow-100 text-yellow-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <div ref={searchRef} className={`relative ${className}`}>\n      {/* Search Input */}\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n          </svg>\n        </div>\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => query.length >= 2 && setShowResults(true)}\n          placeholder={placeholder}\n          className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n        />\n        {loading && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n            <svg className=\"animate-spin h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n          </div>\n        )}\n      </div>\n\n      {/* Search Filters */}\n      {showFilters && (\n        <div className=\"mt-2 flex flex-wrap gap-2\">\n          {searchTypes.map(type => (\n            <button\n              key={type.value}\n              onClick={() => setSelectedType(type.value)}\n              className={`px-3 py-1 text-sm rounded-full transition-colors ${\n                selectedType === type.value\n                  ? 'bg-green-100 text-green-700 border border-green-300'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-300'\n              }`}\n            >\n              {type.label}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Search Results */}\n      {showResults && (\n        <div className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto\">\n          {results.length > 0 ? (\n            <>\n              <div className=\"px-4 py-2 border-b border-gray-100 text-sm text-gray-600\">\n                {total} result{total !== 1 ? 's' : ''} found\n              </div>\n              {results.map((result) => (\n                <Link\n                  key={`${result.type}-${result.id}`}\n                  href={result.url}\n                  onClick={handleResultClick}\n                  className=\"block px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0\"\n                >\n                  <div className=\"flex items-start space-x-3\">\n                    <span className=\"text-lg\">{getTypeIcon(result.type)}</span>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center space-x-2 mb-1\">\n                        <h4 className=\"text-sm font-medium text-gray-900 truncate\">\n                          {result.title}\n                        </h4>\n                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getTypeColor(result.type)}`}>\n                          {result.type}\n                        </span>\n                      </div>\n                      \n                      {result.content && (\n                        <p className=\"text-sm text-gray-600 line-clamp-2 mb-1\">\n                          {result.content}\n                        </p>\n                      )}\n                      \n                      <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                        {result.student && (\n                          <span>Student: {result.student}</span>\n                        )}\n                        {result.studentClass && (\n                          <span>Class: {result.studentClass}</span>\n                        )}\n                        {result.achievementType && (\n                          <span>Type: {result.achievementType}</span>\n                        )}\n                        {result.date && (\n                          <span>Date: {formatDate(result.date)}</span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </Link>\n              ))}\n              \n              {total > results.length && (\n                <div className=\"px-4 py-2 text-center text-sm text-gray-500 border-t border-gray-100\">\n                  Showing {results.length} of {total} results\n                </div>\n              )}\n            </>\n          ) : query.length >= 2 && !loading ? (\n            <div className=\"px-4 py-8 text-center text-gray-500\">\n              <svg className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n              </svg>\n              <p>No results found for &quot;{query}&quot;</p>\n              <p className=\"text-sm mt-1\">Try different keywords or check spelling</p>\n            </div>\n          ) : query.length < 2 ? (\n            <div className=\"px-4 py-6 text-center text-gray-500\">\n              <p>Type at least 2 characters to search</p>\n            </div>\n          ) : null}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAyBe,SAAS,gBAAgB,KAIjB;QAJiB,EACtC,cAAc,gDAAgD,EAC9D,YAAY,EAAE,EACd,cAAc,IAAI,EACG,GAJiB;;IAKtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,cAAc;QAClB;YAAE,OAAO;YAAO,OAAO;QAAc;QACrC;YAAE,OAAO;YAAiB,OAAO;QAAgB;QACjD;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAgB,OAAO;QAAe;KAChD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC1E,eAAe;oBACjB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;oCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,gBAAgB;2DAAW;oBAC/B,IAAI,MAAM,IAAI,GAAG,MAAM,IAAI,GAAG;wBAC5B;oBACF,OAAO;wBACL,WAAW,EAAE;wBACb,eAAe;oBACjB;gBACF;0DAAG;YAEH;6CAAO,IAAM,aAAa;;QAC5B;oCAAG;QAAC;QAAO;KAAa;IAExB,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,AAAC,iBAAkD,OAAlC,mBAAmB,QAAO,UAAqB,OAAb,cAAa;YAElE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,KAAK,OAAO,IAAI,EAAE;gBAC7B,SAAS,KAAK,KAAK,IAAI;gBACvB,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,eAAe;QACf,SAAS;IACX;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,6LAAC;QAAI,KAAK;QAAW,WAAW,AAAC,YAAqB,OAAV;;0BAE1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,MAAM,MAAM,IAAI,KAAK,eAAe;wBACnD,aAAa;wBACb,WAAU;;;;;;oBAEX,yBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAqC,MAAK;4BAAO,SAAQ;;8CACtE,6LAAC;oCAAO,WAAU;oCAAa,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAe,aAAY;;;;;;8CACxF,6LAAC;oCAAK,WAAU;oCAAa,MAAK;oCAAe,GAAE;;;;;;;;;;;;;;;;;;;;;;;YAO1D,6BACC,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;wBAEC,SAAS,IAAM,gBAAgB,KAAK,KAAK;wBACzC,WAAW,AAAC,oDAIX,OAHC,iBAAiB,KAAK,KAAK,GACvB,wDACA;kCAGL,KAAK,KAAK;uBARN,KAAK,KAAK;;;;;;;;;;YAetB,6BACC,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,MAAM,GAAG,kBAChB;;sCACE,6LAAC;4BAAI,WAAU;;gCACZ;gCAAM;gCAAQ,UAAU,IAAI,MAAM;gCAAG;;;;;;;wBAEvC,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,OAAO,GAAG;gCAChB,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAW,YAAY,OAAO,IAAI;;;;;;sDAClD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,OAAO,KAAK;;;;;;sEAEf,6LAAC;4DAAK,WAAW,AAAC,yEAAkG,OAA1B,aAAa,OAAO,IAAI;sEAC/G,OAAO,IAAI;;;;;;;;;;;;gDAIf,OAAO,OAAO,kBACb,6LAAC;oDAAE,WAAU;8DACV,OAAO,OAAO;;;;;;8DAInB,6LAAC;oDAAI,WAAU;;wDACZ,OAAO,OAAO,kBACb,6LAAC;;gEAAK;gEAAU,OAAO,OAAO;;;;;;;wDAE/B,OAAO,YAAY,kBAClB,6LAAC;;gEAAK;gEAAQ,OAAO,YAAY;;;;;;;wDAElC,OAAO,eAAe,kBACrB,6LAAC;;gEAAK;gEAAO,OAAO,eAAe;;;;;;;wDAEpC,OAAO,IAAI,kBACV,6LAAC;;gEAAK;gEAAO,WAAW,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;+BAlCtC,AAAC,GAAiB,OAAf,OAAO,IAAI,EAAC,KAAa,OAAV,OAAO,EAAE;;;;;wBA0CnC,QAAQ,QAAQ,MAAM,kBACrB,6LAAC;4BAAI,WAAU;;gCAAuE;gCAC3E,QAAQ,MAAM;gCAAC;gCAAK;gCAAM;;;;;;;;mCAIvC,MAAM,MAAM,IAAI,KAAK,CAAC,wBACxB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAuC,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9F,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,6LAAC;;gCAAE;gCAA4B;gCAAM;;;;;;;sCACrC,6LAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;2BAE5B,MAAM,MAAM,GAAG,kBACjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCAAE;;;;;;;;;;2BAEH;;;;;;;;;;;;AAKd;GAnOwB;KAAA", "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState } from \"react\";\nimport SearchComponent from \"./SearchComponent\";\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-sm\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-2xl font-bold text-green-900\">\n                Meritorious School Ludhiana\n              </h1>\n              <p className=\"text-sm text-gray-600\">Government of Punjab</p>\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              <Link href=\"/\" className=\"text-green-900 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium\">\n                Home\n              </Link>\n              <Link href=\"/about\" className=\"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium\">\n                About\n              </Link>\n              <Link href=\"/academics\" className=\"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium\">\n                Academics\n              </Link>\n              <Link href=\"/gallery\" className=\"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium\">\n                Gallery\n              </Link>\n              <Link href=\"/faculty\" className=\"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium\">\n                Faculty\n              </Link>\n              <Link href=\"/admissions\" className=\"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium\">\n                Admissions\n              </Link>\n              <Link href=\"/contact\" className=\"text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium\">\n                Contact\n              </Link>\n            </div>\n          </nav>\n\n          {/* Search Component */}\n          <div className=\"hidden md:block ml-6\">\n            <SearchComponent\n              placeholder=\"Search...\"\n              className=\"w-64\"\n              showFilters={false}\n            />\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-600 hover:text-green-700 focus:outline-none focus:text-green-700\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                {isMenuOpen ? (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                ) : (\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 rounded-lg mb-4\">\n              <Link\n                href=\"/\"\n                className=\"text-green-900 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                About\n              </Link>\n              <Link\n                href=\"/academics\"\n                className=\"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Academics\n              </Link>\n              <Link\n                href=\"/gallery\"\n                className=\"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Gallery\n              </Link>\n              <Link\n                href=\"/faculty\"\n                className=\"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Faculty\n              </Link>\n              <Link\n                href=\"/admissions\"\n                className=\"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Admissions\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Contact\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAGlD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAA+E;;;;;;kDAGxG,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA8E;;;;;;kDAG5G,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAA8E;;;;;;kDAGhH,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8E;;;;;;kDAG9G,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8E;;;;;;kDAG9G,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAA8E;;;;;;kDAGjH,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA8E;;;;;;;;;;;;;;;;;sCAOlH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,wIAAA,CAAA,UAAe;gCACd,aAAY;gCACZ,WAAU;gCACV,aAAa;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC7D,2BACC,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;6DAErE,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ9E,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhIwB;KAAA", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/data.ts"], "sourcesContent": ["// Simple data service that works with or without Supabase\n// When Supabase is not configured, it returns mock data\n\nimport { Database } from '@/types/database';\n\ntype SchoolContent = Database['public']['Tables']['school_content']['Row'];\ntype Announcement = Database['public']['Tables']['announcements']['Row'];\ntype GalleryItem = Database['public']['Tables']['gallery']['Row'];\ntype Faculty = Database['public']['Tables']['faculty']['Row'];\ntype Achievement = Database['public']['Tables']['achievements']['Row'];\n\n// Check if Supabase is configured\nconst isSupabaseConfigured = () => {\n  return process.env.NEXT_PUBLIC_SUPABASE_URL &&\n         process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&\n         process.env.NEXT_PUBLIC_SUPABASE_URL !== 'your_supabase_project_url' &&\n         process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY !== 'your_supabase_anon_key' &&\n         process.env.NEXT_PUBLIC_SUPABASE_URL.includes('supabase.co');\n};\n\n// Import Supabase client\nimport { createClient } from '@supabase/supabase-js';\n\nconst getSupabaseClient = () => {\n  if (!isSupabaseConfigured()) {\n    throw new Error('Supabase not configured');\n  }\n  return createClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  );\n};\n\n// Mock data for when Supabase is not configured\nconst mockAnnouncements: Announcement[] = [\n  {\n    id: '1',\n    title: 'Admission 2024-25 Open',\n    content: 'Applications are now open for admission to Class 11th for the academic year 2024-25. Eligible students can apply online through our official website.',\n    priority: 'high',\n    status: 'published',\n    is_published: true,\n    publish_date: '2024-03-01T10:00:00Z',\n    created_at: '2024-03-01T10:00:00Z',\n    updated_at: '2024-03-01T10:00:00Z'\n  },\n  {\n    id: '2',\n    title: 'Annual Sports Day',\n    content: 'Annual Sports Day will be held on March 15, 2024. All students are encouraged to participate in various sporting events.',\n    priority: 'medium',\n    status: 'published',\n    is_published: true,\n    publish_date: '2024-02-20T09:00:00Z',\n    created_at: '2024-02-20T09:00:00Z',\n    updated_at: '2024-02-20T09:00:00Z'\n  }\n];\n\nconst mockAchievements: Achievement[] = [\n  {\n    id: '1',\n    title: 'JEE Advanced Qualification',\n    description: 'Student qualified for JEE Advanced 2023 with All India Rank under 5000',\n    student_name: 'Arjun Sharma',\n    student_class: 'Class 12',\n    achievement_type: 'academic',\n    achievement_date: '2023-06-15',\n    award_by: 'IIT Council',\n    position: 'AIR 4500',\n    image_url: undefined,\n    certificate_url: undefined,\n    status: 'published',\n    is_published: true,\n    created_at: '2023-06-15T00:00:00Z',\n    updated_at: '2023-06-15T00:00:00Z'\n  },\n  {\n    id: '2',\n    title: 'NEET Top Scorer',\n    description: 'Achieved 650+ marks in NEET 2023, securing admission in government medical college',\n    student_name: 'Priya Kaur',\n    student_class: 'Class 12',\n    achievement_type: 'academic',\n    achievement_date: '2023-05-20',\n    award_by: 'NTA',\n    position: '650/720',\n    image_url: undefined,\n    certificate_url: undefined,\n    status: 'published',\n    is_published: true,\n    created_at: '2023-05-20T00:00:00Z',\n    updated_at: '2023-05-20T00:00:00Z'\n  }\n];\n\nconst mockFaculty: Faculty[] = [\n  {\n    id: '1',\n    name: 'Dr. Rajesh Kumar',\n    designation: 'Principal',\n    department: 'Administration',\n    qualification: 'Ph.D. in Education, M.A. Education',\n    experience_years: 25,\n    image_url: undefined,\n    email: '<EMAIL>',\n    phone: undefined,\n    bio: 'Dr. Rajesh Kumar brings over 25 years of experience in educational administration.',\n    is_published: true,\n    order_index: 1,\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z'\n  }\n];\n\n\n\n// Simplified functions that return mock data for now\n// These will be updated when Supabase is properly configured\n\nexport async function getLatestAnnouncements(limit: number = 5): Promise<Announcement[]> {\n  if (!isSupabaseConfigured()) {\n    return mockAnnouncements.slice(0, limit);\n  }\n\n  try {\n    const supabase = getSupabaseClient();\n    const { data, error } = await supabase\n      .from('announcements')\n      .select('*')\n      .eq('is_published', true)\n      .order('publish_date', { ascending: false })\n      .limit(limit);\n\n    if (error) {\n      console.error('Error fetching announcements:', error);\n      return mockAnnouncements.slice(0, limit);\n    }\n\n    return data || mockAnnouncements.slice(0, limit);\n  } catch (err) {\n    console.error('Supabase error:', err);\n    return mockAnnouncements.slice(0, limit);\n  }\n}\n\nexport async function getAnnouncementsByPriority(priority: 'low' | 'medium' | 'high'): Promise<Announcement[]> {\n  return mockAnnouncements.filter(a => a.priority === priority);\n}\n\n// Gallery Functions\nexport async function getGalleryByCategory(_category: string): Promise<GalleryItem[]> {\n  return []; // Return empty for now\n}\n\nexport async function getAllGalleryItems(_limit?: number): Promise<GalleryItem[]> {\n  return []; // Return empty for now\n}\n\nexport async function getGalleryCategories(): Promise<string[]> {\n  return []; // Return empty for now\n}\n\n// Faculty Functions\nexport async function getFacultyByDepartment(department: string): Promise<Faculty[]> {\n  return mockFaculty.filter(f => f.department === department);\n}\n\nexport async function getAllFaculty(): Promise<Faculty[]> {\n  if (!isSupabaseConfigured()) {\n    return mockFaculty;\n  }\n\n  try {\n    const supabase = getSupabaseClient();\n    const { data, error } = await supabase\n      .from('faculty')\n      .select('*')\n      .eq('is_published', true)\n      .order('order_index', { ascending: true });\n\n    if (error) {\n      console.error('Error fetching faculty:', error);\n      return mockFaculty;\n    }\n\n    return data || mockFaculty;\n  } catch (err) {\n    console.error('Supabase error:', err);\n    return mockFaculty;\n  }\n}\n\n// Achievements Functions\nexport async function getAchievementsByCategory(category: string): Promise<Achievement[]> {\n  return mockAchievements.filter(a => a.achievement_type === category);\n}\n\nexport async function getLatestAchievements(limit: number = 6): Promise<Achievement[]> {\n  if (!isSupabaseConfigured()) {\n    return mockAchievements.slice(0, limit);\n  }\n\n  try {\n    const supabase = getSupabaseClient();\n    const { data, error } = await supabase\n      .from('achievements')\n      .select('*')\n      .eq('is_published', true)\n      .order('achievement_date', { ascending: false })\n      .limit(limit);\n\n    if (error) {\n      console.error('Error fetching achievements:', error);\n      return mockAchievements.slice(0, limit);\n    }\n\n    return data || mockAchievements.slice(0, limit);\n  } catch (err) {\n    console.error('Supabase error:', err);\n    return mockAchievements.slice(0, limit);\n  }\n}\n\nexport async function getAchievementCategories(): Promise<string[]> {\n  const categories = [...new Set(mockAchievements.map(item => item.achievement_type))];\n  return categories;\n}\n\n// School Content Functions\nexport async function getSchoolContentBySection(_section: string): Promise<SchoolContent[]> {\n  return []; // Return empty for now\n}\n\nexport async function getAllSchoolContent(): Promise<SchoolContent[]> {\n  return []; // Return empty for now\n}\n\n// Search Functions\nexport async function searchContent(query: string): Promise<{\n  content: SchoolContent[];\n  announcements: Announcement[];\n  faculty: Faculty[];\n}> {\n  const lowerQuery = query.toLowerCase();\n\n  return {\n    content: [],\n    announcements: mockAnnouncements.filter(a =>\n      a.title.toLowerCase().includes(lowerQuery) ||\n      a.content.toLowerCase().includes(lowerQuery)\n    ),\n    faculty: mockFaculty.filter(f =>\n      f.name.toLowerCase().includes(lowerQuery) ||\n      f.designation.toLowerCase().includes(lowerQuery) ||\n      (f.department && f.department.toLowerCase().includes(lowerQuery))\n    )\n  };\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,wDAAwD;;;;;;;;;;;;;;;;AAY/C;AAOT,yBAAyB;AACzB;AAVA,kCAAkC;AAClC,MAAM,uBAAuB;IAC3B,OAAO,wUAEA,iFAAyC,+BACzC,yPAA8C,4BAC9C,6EAAqC,QAAQ,CAAC;AACvD;;AAKA,MAAM,oBAAoB;IACxB,IAAI,CAAC,wBAAwB;QAC3B,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD;AAIpB;AAEA,gDAAgD;AAChD,MAAM,oBAAoC;IACxC;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,cAAc;QACd,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,cAAc;QACd,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAED,MAAM,mBAAkC;IACtC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,kBAAkB;QAClB,UAAU;QACV,UAAU;QACV,WAAW;QACX,iBAAiB;QACjB,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,kBAAkB;QAClB,UAAU;QACV,UAAU;QACV,WAAW;QACX,iBAAiB;QACjB,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAED,MAAM,cAAyB;IAC7B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,YAAY;QACZ,eAAe;QACf,kBAAkB;QAClB,WAAW;QACX,OAAO;QACP,OAAO;QACP,KAAK;QACL,cAAc;QACd,aAAa;QACb,YAAY;QACZ,YAAY;IACd;CACD;AAOM,eAAe;QAAuB,QAAA,iEAAgB;IAC3D,IAAI,CAAC,wBAAwB;QAC3B,OAAO,kBAAkB,KAAK,CAAC,GAAG;IACpC;IAEA,IAAI;QACF,MAAM,WAAW;QACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,gBAAgB,MACnB,KAAK,CAAC,gBAAgB;YAAE,WAAW;QAAM,GACzC,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,kBAAkB,KAAK,CAAC,GAAG;QACpC;QAEA,OAAO,QAAQ,kBAAkB,KAAK,CAAC,GAAG;IAC5C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,kBAAkB,KAAK,CAAC,GAAG;IACpC;AACF;AAEO,eAAe,2BAA2B,QAAmC;IAClF,OAAO,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;AACtD;AAGO,eAAe,qBAAqB,SAAiB;IAC1D,OAAO,EAAE,EAAE,uBAAuB;AACpC;AAEO,eAAe,mBAAmB,MAAe;IACtD,OAAO,EAAE,EAAE,uBAAuB;AACpC;AAEO,eAAe;IACpB,OAAO,EAAE,EAAE,uBAAuB;AACpC;AAGO,eAAe,uBAAuB,UAAkB;IAC7D,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;AAClD;AAEO,eAAe;IACpB,IAAI,CAAC,wBAAwB;QAC3B,OAAO;IACT;IAEA,IAAI;QACF,MAAM,WAAW;QACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,gBAAgB,MACnB,KAAK,CAAC,eAAe;YAAE,WAAW;QAAK;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;QAEA,OAAO,QAAQ;IACjB,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO;IACT;AACF;AAGO,eAAe,0BAA0B,QAAgB;IAC9D,OAAO,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,gBAAgB,KAAK;AAC7D;AAEO,eAAe;QAAsB,QAAA,iEAAgB;IAC1D,IAAI,CAAC,wBAAwB;QAC3B,OAAO,iBAAiB,KAAK,CAAC,GAAG;IACnC;IAEA,IAAI;QACF,MAAM,WAAW;QACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,gBAAgB,MACnB,KAAK,CAAC,oBAAoB;YAAE,WAAW;QAAM,GAC7C,KAAK,CAAC;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,iBAAiB,KAAK,CAAC,GAAG;QACnC;QAEA,OAAO,QAAQ,iBAAiB,KAAK,CAAC,GAAG;IAC3C,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,iBAAiB,KAAK,CAAC,GAAG;IACnC;AACF;AAEO,eAAe;IACpB,MAAM,aAAa;WAAI,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,OAAQ,KAAK,gBAAgB;KAAG;IACpF,OAAO;AACT;AAGO,eAAe,0BAA0B,QAAgB;IAC9D,OAAO,EAAE,EAAE,uBAAuB;AACpC;AAEO,eAAe;IACpB,OAAO,EAAE,EAAE,uBAAuB;AACpC;AAGO,eAAe,cAAc,KAAa;IAK/C,MAAM,aAAa,MAAM,WAAW;IAEpC,OAAO;QACL,SAAS,EAAE;QACX,eAAe,kBAAkB,MAAM,CAAC,CAAA,IACtC,EAAE,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC/B,EAAE,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;QAEnC,SAAS,YAAY,MAAM,CAAC,CAAA,IAC1B,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC9B,EAAE,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eACpC,EAAE,UAAU,IAAI,EAAE,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC;IAEzD;AACF", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Announcements.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { getLatestAnnouncements } from '@/lib/data';\nimport { Database } from '@/types/database';\n\ntype Announcement = Database['public']['Tables']['announcements']['Row'];\n\nexport default function Announcements() {\n  const [announcements, setAnnouncements] = useState<Announcement[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    async function fetchAnnouncements() {\n      try {\n        const data = await getLatestAnnouncements(3);\n        setAnnouncements(data);\n      } catch (err) {\n        setError('Failed to load announcements');\n        console.error('Error fetching announcements:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n\n    fetchAnnouncements();\n  }, []);\n\n  if (loading) {\n    return (\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Latest Announcements</h2>\n            <div className=\"animate-pulse space-y-4\">\n              {[1, 2, 3].map((i) => (\n                <div key={i} className=\"bg-white p-6 rounded-lg shadow-md\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-4\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-full mb-2\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error) {\n    return (\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Latest Announcements</h2>\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\n              <p className=\"text-red-600\">{error}</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (announcements.length === 0) {\n    return (\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Latest Announcements</h2>\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n              <p className=\"text-blue-600\">No announcements available at the moment.</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'medium':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'low':\n        return 'bg-green-100 text-green-800 border-green-200';\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-IN', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Latest Announcements</h2>\n          <div className=\"w-24 h-1 bg-green-600 mx-auto\"></div>\n        </div>\n        \n        <div className=\"space-y-6\">\n          {announcements.map((announcement) => (\n            <div key={announcement.id} className=\"bg-white rounded-lg shadow-md overflow-hidden\">\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 flex-1\">\n                    {announcement.title}\n                  </h3>\n                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(announcement.priority)}`}>\n                    {announcement.priority.toUpperCase()}\n                  </span>\n                </div>\n                \n                <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                  {announcement.content}\n                </p>\n                \n                <div className=\"flex items-center text-sm text-gray-500\">\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                  Published on {formatDate(announcement.publish_date)}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n        \n        <div className=\"text-center mt-8\">\n          <button className=\"bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors\">\n            View All Announcements\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAQe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,eAAe;gBACb,IAAI;oBACF,MAAM,OAAO,MAAM,CAAA,GAAA,qHAAA,CAAA,yBAAsB,AAAD,EAAE;oBAC1C,iBAAiB;gBACnB,EAAE,OAAO,KAAK;oBACZ,SAAS;oBACT,QAAQ,KAAK,CAAC,iCAAiC;gBACjD,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;kCAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;oCAAY,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;mCAJP;;;;;;;;;;;;;;;;;;;;;;;;;;IAYxB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMzC;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMzC;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;4BAA0B,WAAU;sCACnC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,aAAa,KAAK;;;;;;0DAErB,6LAAC;gDAAK,WAAW,AAAC,qDAA4F,OAAxC,iBAAiB,aAAa,QAAQ;0DACzG,aAAa,QAAQ,CAAC,WAAW;;;;;;;;;;;;kDAItC,6LAAC;wCAAE,WAAU;kDACV,aAAa,OAAO;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;4CACQ,WAAW,aAAa,YAAY;;;;;;;;;;;;;2BAnB9C,aAAa,EAAE;;;;;;;;;;8BA0B7B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAO,WAAU;kCAAkG;;;;;;;;;;;;;;;;;;;;;;AAO9H;GA3IwB;KAAA", "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "sources": ["file:///Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Achievements.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { getLatestAchievements } from '@/lib/data';\nimport { Database } from '@/types/database';\n\ntype Achievement = Database['public']['Tables']['achievements']['Row'];\n\nexport default function Achievements() {\n  const [achievements, setAchievements] = useState<Achievement[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    async function fetchAchievements() {\n      try {\n        const data = await getLatestAchievements(6);\n        setAchievements(data);\n      } catch (err) {\n        setError('Failed to load achievements');\n        console.error('Error fetching achievements:', err);\n      } finally {\n        setLoading(false);\n      }\n    }\n\n    fetchAchievements();\n  }, []);\n\n  if (loading) {\n    return (\n      <section className=\"py-16 bg-green-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Student Achievements</h2>\n            <div className=\"w-24 h-1 bg-green-600 mx-auto\"></div>\n          </div>\n          \n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {[1, 2, 3, 4, 5, 6].map((i) => (\n              <div key={i} className=\"bg-white rounded-lg p-6 shadow-md animate-pulse\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-full mb-2\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (error) {\n    return (\n      <section className=\"py-16 bg-green-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Student Achievements</h2>\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\n              <p className=\"text-red-600\">{error}</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  if (achievements.length === 0) {\n    return (\n      <section className=\"py-16 bg-green-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-8\">Student Achievements</h2>\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n              <p className=\"text-blue-600\">Achievement records will be updated soon.</p>\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  const getCategoryIcon = (category: string) => {\n    switch (category.toLowerCase()) {\n      case 'academic':\n        return (\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n          </svg>\n        );\n      case 'sports':\n        return (\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n          </svg>\n        );\n      case 'cultural':\n        return (\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 3v10a2 2 0 002 2h6a2 2 0 002-2V7M7 7h10M9 11h6M9 15h6\" />\n          </svg>\n        );\n      default:\n        return (\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\" />\n          </svg>\n        );\n    }\n  };\n\n  const getCategoryColor = (category: string) => {\n    switch (category.toLowerCase()) {\n      case 'academic':\n        return 'bg-blue-100 text-blue-800';\n      case 'sports':\n        return 'bg-green-100 text-green-800';\n      case 'cultural':\n        return 'bg-purple-100 text-purple-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const formatDate = (dateString: string | null) => {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-IN', {\n      year: 'numeric',\n      month: 'short'\n    });\n  };\n\n  return (\n    <section className=\"py-16 bg-green-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Student Achievements</h2>\n          <div className=\"w-24 h-1 bg-green-600 mx-auto mb-6\"></div>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Celebrating the outstanding accomplishments of our students in academics, sports, and cultural activities.\n          </p>\n        </div>\n        \n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {achievements.map((achievement) => (\n            <div key={achievement.id} className=\"bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow\">\n              <div className=\"flex items-start justify-between mb-4\">\n                <div className={`p-2 rounded-lg ${getCategoryColor(achievement.achievement_type)}`}>\n                  {getCategoryIcon(achievement.achievement_type)}\n                </div>\n                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(achievement.achievement_type)}`}>\n                  {achievement.achievement_type.charAt(0).toUpperCase() + achievement.achievement_type.slice(1)}\n                </span>\n              </div>\n              \n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {achievement.title}\n              </h3>\n              \n              <p className=\"text-gray-600 text-sm mb-4 leading-relaxed\">\n                {achievement.description}\n              </p>\n              \n              {achievement.student_name && (\n                <div className=\"flex items-center text-sm text-gray-700 mb-2\">\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                  {achievement.student_name}\n                </div>\n              )}\n              \n              {achievement.achievement_date && (\n                <div className=\"flex items-center text-sm text-gray-500\">\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                  </svg>\n                  {formatDate(achievement.achievement_date)}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n        \n        <div className=\"text-center mt-12\">\n          <button className=\"bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors\">\n            View All Achievements\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAQe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,eAAe;gBACb,IAAI;oBACF,MAAM,OAAO,MAAM,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,EAAE;oBACzC,gBAAgB;gBAClB,EAAE,OAAO,KAAK;oBACZ,SAAS;oBACT,QAAQ,KAAK,CAAC,gCAAgC;gBAChD,SAAU;oBACR,WAAW;gBACb;YACF;YAEA;QACF;iCAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACvB,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;+BAJP;;;;;;;;;;;;;;;;;;;;;IAWtB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMzC;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMzC;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;gBACE,qBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;QAG7E;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;4BAAyB,WAAU;;8CAClC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,kBAAgE,OAA/C,iBAAiB,YAAY,gBAAgB;sDAC5E,gBAAgB,YAAY,gBAAgB;;;;;;sDAE/C,6LAAC;4CAAK,WAAW,AAAC,8CAA4F,OAA/C,iBAAiB,YAAY,gBAAgB;sDACzG,YAAY,gBAAgB,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,YAAY,gBAAgB,CAAC,KAAK,CAAC;;;;;;;;;;;;8CAI/F,6LAAC;oCAAG,WAAU;8CACX,YAAY,KAAK;;;;;;8CAGpB,6LAAC;oCAAE,WAAU;8CACV,YAAY,WAAW;;;;;;gCAGzB,YAAY,YAAY,kBACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAEtE,YAAY,YAAY;;;;;;;gCAI5B,YAAY,gBAAgB,kBAC3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCAEtE,WAAW,YAAY,gBAAgB;;;;;;;;2BAhCpC,YAAY,EAAE;;;;;;;;;;8BAuC5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAO,WAAU;kCAAkG;;;;;;;;;;;;;;;;;;;;;;AAO9H;GA1LwB;KAAA", "debugId": null}}]}