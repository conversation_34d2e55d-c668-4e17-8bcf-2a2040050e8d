(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5407],{1280:(e,t,i)=>{"use strict";i.d(t,{A:()=>h});var s=i(5155),r=i(5109),n=i(1652),l=i(1891),o=i(6761),c=i(4109),a=i(4589),d=i(6377),u=i(2115);function h(e){let{content:t,onChange:i,placeholder:h="Start writing...",className:g=""}=e,[m,x]=(0,u.useState)(!1),[b,p]=(0,u.useState)(""),f=(0,r.hG)({extensions:[n.A,l.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),o.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-green-600 underline hover:text-green-700"}}),c.<PERSON>.configure({types:["heading","paragraph"]}),a.A,d.xJ],content:t,onUpdate:e=>{let{editor:t}=e;i(t.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}}});if(!f)return null;let v=e=>{let{onClick:t,isActive:i=!1,disabled:r=!1,children:n,title:l}=e;return(0,s.jsx)("button",{type:"button",onClick:t,disabled:r,title:l,className:"p-2 rounded text-sm font-medium transition-colors ".concat(i?"bg-green-100 text-green-700 border border-green-300":"text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent"," ").concat(r?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:n})};return(0,s.jsxs)("div",{className:"border border-gray-300 rounded-lg overflow-hidden ".concat(g),children:[(0,s.jsxs)("div",{className:"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,s.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleBold().run(),isActive:f.isActive("bold"),title:"Bold",children:(0,s.jsx)("strong",{children:"B"})}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleItalic().run(),isActive:f.isActive("italic"),title:"Italic",children:(0,s.jsx)("em",{children:"I"})}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleStrike().run(),isActive:f.isActive("strike"),title:"Strikethrough",children:(0,s.jsx)("s",{children:"S"})})]}),(0,s.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:1}).run(),isActive:f.isActive("heading",{level:1}),title:"Heading 1",children:"H1"}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:2}).run(),isActive:f.isActive("heading",{level:2}),title:"Heading 2",children:"H2"}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:3}).run(),isActive:f.isActive("heading",{level:3}),title:"Heading 3",children:"H3"})]}),(0,s.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleBulletList().run(),isActive:f.isActive("bulletList"),title:"Bullet List",children:"•"}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleOrderedList().run(),isActive:f.isActive("orderedList"),title:"Numbered List",children:"1."})]}),(0,s.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,s.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("left").run(),isActive:f.isActive({textAlign:"left"}),title:"Align Left",children:"⬅"}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("center").run(),isActive:f.isActive({textAlign:"center"}),title:"Align Center",children:"↔"}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("right").run(),isActive:f.isActive({textAlign:"right"}),title:"Align Right",children:"➡"})]}),(0,s.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,s.jsx)(v,{onClick:()=>{p(f.getAttributes("link").href||""),x(!0)},isActive:f.isActive("link"),title:"Add Link",children:"\uD83D\uDD17"}),(0,s.jsx)(v,{onClick:()=>{let e=window.prompt("Enter image URL:");e&&f.chain().focus().setImage({src:e}).run()},title:"Add Image",children:"\uD83D\uDDBC"})]}),(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)(v,{onClick:()=>f.chain().focus().toggleBlockquote().run(),isActive:f.isActive("blockquote"),title:"Quote",children:'"'}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().setHorizontalRule().run(),title:"Horizontal Rule",children:"―"}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().undo().run(),disabled:!f.can().chain().focus().undo().run(),title:"Undo",children:"↶"}),(0,s.jsx)(v,{onClick:()=>f.chain().focus().redo().run(),disabled:!f.can().chain().focus().redo().run(),title:"Redo",children:"↷"})]})]}),(0,s.jsxs)("div",{className:"min-h-[200px] bg-white",children:[(0,s.jsx)(r.$Z,{editor:f,className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"}),f.isEmpty&&(0,s.jsx)("div",{className:"absolute top-16 left-4 text-gray-400 pointer-events-none",children:h})]}),m&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add Link"}),(0,s.jsx)("input",{type:"url",value:b,onChange:e=>p(e.target.value),placeholder:"Enter URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 mb-4",autoFocus:!0}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)("button",{type:"button",onClick:()=>x(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,s.jsx)("button",{type:"button",onClick:()=>{""===b?f.chain().focus().extendMarkRange("link").unsetLink().run():f.chain().focus().extendMarkRange("link").setLink({href:b}).run(),x(!1),p("")},className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700",children:"Add Link"})]})]})})]})}},5407:(e,t,i)=>{Promise.resolve().then(i.bind(i,6071)),Promise.resolve().then(i.bind(i,8807))},8807:(e,t,i)=>{"use strict";i.d(t,{default:()=>a});var s=i(5155),r=i(2115),n=i(5695),l=i(8907),o=i(603),c=i(1280);function a(e){let{announcement:t,isEdit:i=!1}=e,a=(0,n.useRouter)(),[d,u]=(0,r.useState)(!1),[h,g]=(0,r.useState)(""),[m,x]=(0,r.useState)({title:(null==t?void 0:t.title)||"",content:(null==t?void 0:t.content)||"",priority:(null==t?void 0:t.priority)||"medium",status:(null==t?void 0:t.status)||(0,l.F9)(),is_published:(null==t?void 0:t.is_published)||!1,publish_date:(null==t?void 0:t.publish_date)?new Date(t.publish_date).toISOString().slice(0,16):new Date().toISOString().slice(0,16),is_scheduled:!1,scheduled_publish_date:""}),[b,p]=(0,r.useState)([]),[f,v]=(0,r.useState)(!1),y=async e=>{e.preventDefault(),u(!0),g("");try{let e=i?"/api/admin/announcements/".concat(null==t?void 0:t.id):"/api/admin/announcements",s=await fetch(e,{method:i?"PATCH":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...m,publish_date:new Date(m.publish_date).toISOString(),attachments:b})});if(s.ok)a.push("/admin/announcements");else{let e=await s.json();g(e.error||"Failed to save announcement")}}catch(e){g("An error occurred. Please try again.")}finally{u(!1)}},j=async()=>{x({...m,is_published:!1});let e=document.getElementById("announcement-form");e&&e.requestSubmit()};return(0,s.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,s.jsxs)("form",{id:"announcement-form",onSubmit:y,className:"space-y-6 p-6",children:[h&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:h}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"Title *"}),(0,s.jsx)("input",{type:"text",id:"title",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:m.title,onChange:e=>x({...m,title:e.target.value}),placeholder:"Enter announcement title"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-2",children:"Content *"}),(0,s.jsx)(c.A,{content:m.content,onChange:e=>x({...m,content:e}),placeholder:"Enter announcement content...",className:"min-h-[300px]"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"priority",className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority"}),(0,s.jsxs)("select",{id:"priority",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:m.priority,onChange:e=>x({...m,priority:e.target.value}),children:[(0,s.jsx)("option",{value:"low",children:"Low"}),(0,s.jsx)("option",{value:"medium",children:"Medium"}),(0,s.jsx)("option",{value:"high",children:"High"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,s.jsxs)("select",{id:"status",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:m.status,onChange:e=>x({...m,status:e.target.value}),children:[(0,s.jsx)("option",{value:"new",children:"New"}),(0,s.jsx)("option",{value:"draft",children:"Draft"}),(0,s.jsx)("option",{value:"published",children:"Published"}),(0,s.jsx)("option",{value:"archived",children:"Archived"})]}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:(0,l.Ng)(m.status).description})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"publish_date",className:"block text-sm font-medium text-gray-700 mb-2",children:"Publish Date"}),(0,s.jsx)("input",{type:"datetime-local",id:"publish_date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:m.publish_date,onChange:e=>x({...m,publish_date:e.target.value})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Attachments (Optional)"}),(0,s.jsx)(o.A,{bucket:"announcements",folder:"attachments",validationType:"any",multiple:!0,maxFiles:5,onUploadStart:()=>v(!0),onUploadComplete:e=>{v(!1);let t=e.filter(e=>e.success&&e.url).map(e=>e.url);p(e=>[...e,...t])},className:"mb-4"}),b.length>0&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Attached Files:"}),b.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded",children:[(0,s.jsx)("span",{className:"text-sm text-gray-700 truncate",children:e.split("/").pop()}),(0,s.jsx)("button",{type:"button",onClick:()=>p(e=>e.filter((e,i)=>i!==t)),className:"text-red-600 hover:text-red-800 text-sm",children:"Remove"})]},t))]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"is_published",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:m.is_published&&!m.is_scheduled,onChange:e=>x({...m,is_published:e.target.checked,is_scheduled:!1})}),(0,s.jsx)("label",{htmlFor:"is_published",className:"ml-2 block text-sm text-gray-700",children:"Publish immediately"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"is_scheduled",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:m.is_scheduled,onChange:e=>x({...m,is_scheduled:e.target.checked,is_published:!1})}),(0,s.jsx)("label",{htmlFor:"is_scheduled",className:"ml-2 block text-sm text-gray-700",children:"Schedule for later"})]}),m.is_scheduled&&(0,s.jsxs)("div",{className:"ml-6",children:[(0,s.jsx)("label",{htmlFor:"scheduled_publish_date",className:"block text-sm font-medium text-gray-700 mb-2",children:"Scheduled Publish Date & Time"}),(0,s.jsx)("input",{type:"datetime-local",id:"scheduled_publish_date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:m.scheduled_publish_date,onChange:e=>x({...m,scheduled_publish_date:e.target.value}),min:new Date().toISOString().slice(0,16)}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"The announcement will be automatically published at the scheduled time"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,s.jsx)("button",{type:"button",onClick:()=>a.back(),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Cancel"}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[!i&&(0,s.jsx)("button",{type:"button",onClick:j,disabled:d,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:"Save as Draft"}),(0,s.jsxs)("button",{type:"submit",disabled:d||f,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[f?"Uploading...":d?"Saving...":i?"Update":"Create"," Announcement"]})]})]})]})})}},8907:(e,t,i)=>{"use strict";i.d(t,{F9:()=>l,Ng:()=>r,qm:()=>n});let s={new:{label:"New",color:"text-blue-800",bgColor:"bg-blue-100",description:"Recently created content"},draft:{label:"Draft",color:"text-gray-800",bgColor:"bg-gray-100",description:"Work in progress, not published"},published:{label:"Published",color:"text-green-800",bgColor:"bg-green-100",description:"Live and visible to public"},archived:{label:"Archived",color:"text-yellow-800",bgColor:"bg-yellow-100",description:"Hidden from public, kept for reference"}};function r(e){return s[e]||s.draft}function n(e,t){let i=r(e),s="".concat(i.bgColor," ").concat(i.color);return"".concat("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"," ").concat(s," ").concat(t||"").trim()}function l(){return"new"}}}]);