(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8613],{1280:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var i=r(5155),l=r(5109),s=r(1652),n=r(1891),a=r(6761),o=r(4109),c=r(4589),d=r(6377),u=r(2115);function m(e){let{content:t,onChange:r,placeholder:m="Start writing...",className:g=""}=e,[h,x]=(0,u.useState)(!1),[b,p]=(0,u.useState)(""),f=(0,l.hG)({extensions:[s.A,n.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),a.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-green-600 underline hover:text-green-700"}}),o.A.configure({types:["heading","paragraph"]}),c.A,d.xJ],content:t,onUpdate:e=>{let{editor:t}=e;r(t.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}}});if(!f)return null;let v=e=>{let{onClick:t,isActive:r=!1,disabled:l=!1,children:s,title:n}=e;return(0,i.jsx)("button",{type:"button",onClick:t,disabled:l,title:n,className:"p-2 rounded text-sm font-medium transition-colors ".concat(r?"bg-green-100 text-green-700 border border-green-300":"text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent"," ").concat(l?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:s})};return(0,i.jsxs)("div",{className:"border border-gray-300 rounded-lg overflow-hidden ".concat(g),children:[(0,i.jsxs)("div",{className:"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,i.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleBold().run(),isActive:f.isActive("bold"),title:"Bold",children:(0,i.jsx)("strong",{children:"B"})}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleItalic().run(),isActive:f.isActive("italic"),title:"Italic",children:(0,i.jsx)("em",{children:"I"})}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleStrike().run(),isActive:f.isActive("strike"),title:"Strikethrough",children:(0,i.jsx)("s",{children:"S"})})]}),(0,i.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:1}).run(),isActive:f.isActive("heading",{level:1}),title:"Heading 1",children:"H1"}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:2}).run(),isActive:f.isActive("heading",{level:2}),title:"Heading 2",children:"H2"}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:3}).run(),isActive:f.isActive("heading",{level:3}),title:"Heading 3",children:"H3"})]}),(0,i.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleBulletList().run(),isActive:f.isActive("bulletList"),title:"Bullet List",children:"•"}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleOrderedList().run(),isActive:f.isActive("orderedList"),title:"Numbered List",children:"1."})]}),(0,i.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,i.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("left").run(),isActive:f.isActive({textAlign:"left"}),title:"Align Left",children:"⬅"}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("center").run(),isActive:f.isActive({textAlign:"center"}),title:"Align Center",children:"↔"}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("right").run(),isActive:f.isActive({textAlign:"right"}),title:"Align Right",children:"➡"})]}),(0,i.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,i.jsx)(v,{onClick:()=>{p(f.getAttributes("link").href||""),x(!0)},isActive:f.isActive("link"),title:"Add Link",children:"\uD83D\uDD17"}),(0,i.jsx)(v,{onClick:()=>{let e=window.prompt("Enter image URL:");e&&f.chain().focus().setImage({src:e}).run()},title:"Add Image",children:"\uD83D\uDDBC"})]}),(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(v,{onClick:()=>f.chain().focus().toggleBlockquote().run(),isActive:f.isActive("blockquote"),title:"Quote",children:'"'}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().setHorizontalRule().run(),title:"Horizontal Rule",children:"―"}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().undo().run(),disabled:!f.can().chain().focus().undo().run(),title:"Undo",children:"↶"}),(0,i.jsx)(v,{onClick:()=>f.chain().focus().redo().run(),disabled:!f.can().chain().focus().redo().run(),title:"Redo",children:"↷"})]})]}),(0,i.jsxs)("div",{className:"min-h-[200px] bg-white",children:[(0,i.jsx)(l.$Z,{editor:f,className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"}),f.isEmpty&&(0,i.jsx)("div",{className:"absolute top-16 left-4 text-gray-400 pointer-events-none",children:m})]}),h&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add Link"}),(0,i.jsx)("input",{type:"url",value:b,onChange:e=>p(e.target.value),placeholder:"Enter URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 mb-4",autoFocus:!0}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{type:"button",onClick:()=>x(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,i.jsx)("button",{type:"button",onClick:()=>{""===b?f.chain().focus().extendMarkRange("link").unsetLink().run():f.chain().focus().extendMarkRange("link").setLink({href:b}).run(),x(!1),p("")},className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700",children:"Add Link"})]})]})})]})}},8613:(e,t,r)=>{Promise.resolve().then(r.bind(r,8775)),Promise.resolve().then(r.bind(r,6071))},8775:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var i=r(5155),l=r(2115),s=r(5695),n=r(6766),a=r(603),o=r(1280),c=r(8907);function d(e){let{achievement:t,isEdit:r=!1}=e,d=(0,s.useRouter)(),[u,m]=(0,l.useState)(!1),[g,h]=(0,l.useState)(!1),[x,b]=(0,l.useState)(""),[p,f]=(0,l.useState)({title:(null==t?void 0:t.title)||"",description:(null==t?void 0:t.description)||"",student_name:(null==t?void 0:t.student_name)||"",student_class:(null==t?void 0:t.student_class)||"",achievement_type:(null==t?void 0:t.achievement_type)||"academic",achievement_date:(null==t?void 0:t.achievement_date)?new Date(t.achievement_date).toISOString().slice(0,10):new Date().toISOString().slice(0,10),award_by:(null==t?void 0:t.award_by)||"",position:(null==t?void 0:t.position)||"",image_url:(null==t?void 0:t.image_url)||"",certificate_url:(null==t?void 0:t.certificate_url)||"",status:(null==t?void 0:t.status)||(0,c.F9)(),is_published:(null==t?void 0:t.is_published)===void 0||t.is_published}),v=async e=>{e.preventDefault(),m(!0),b("");try{let e=r?"/api/admin/achievements/".concat(null==t?void 0:t.id):"/api/admin/achievements",i=await fetch(e,{method:r?"PATCH":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)});if(i.ok)d.push("/admin/achievements");else{let e=await i.json();b(e.error||"Failed to save achievement")}}catch(e){b("An error occurred. Please try again.")}finally{m(!1)}};return(0,i.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,i.jsxs)("form",{onSubmit:v,className:"space-y-6 p-6",children:[x&&(0,i.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:x}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Achievement Image (Optional)"}),p.image_url&&(0,i.jsx)("div",{className:"mb-4",children:(0,i.jsx)(n.default,{src:p.image_url,alt:"Achievement image",width:200,height:150,className:"w-48 h-36 rounded object-cover"})}),(0,i.jsx)(a.A,{bucket:"achievements",folder:"images",validationType:"image",multiple:!1,onUploadStart:()=>h(!0),onUploadComplete:e=>{h(!1);let t=e.find(e=>e.success&&e.url);t&&f({...p,image_url:t.url})},className:"mb-4"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Certificate/Document (Optional)"}),p.certificate_url&&(0,i.jsx)("div",{className:"mb-4",children:(0,i.jsxs)("a",{href:p.certificate_url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,i.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"View Certificate"]})}),(0,i.jsx)(a.A,{bucket:"achievements",folder:"certificates",validationType:"document",multiple:!1,onUploadStart:()=>h(!0),onUploadComplete:e=>{h(!1);let t=e.find(e=>e.success&&e.url);t&&f({...p,certificate_url:t.url})},className:"mb-4"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:"Achievement Title *"}),(0,i.jsx)("input",{type:"text",id:"title",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.title,onChange:e=>f({...p,title:e.target.value}),placeholder:"Enter achievement title"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,i.jsx)(o.A,{content:p.description||"",onChange:e=>f({...p,description:e}),placeholder:"Describe the achievement in detail...",className:"min-h-[200px]"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"student_name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Student Name"}),(0,i.jsx)("input",{type:"text",id:"student_name",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.student_name,onChange:e=>f({...p,student_name:e.target.value}),placeholder:"Enter student name"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"student_class",className:"block text-sm font-medium text-gray-700 mb-2",children:"Class"}),(0,i.jsxs)("select",{id:"student_class",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.student_class,onChange:e=>f({...p,student_class:e.target.value}),children:[(0,i.jsx)("option",{value:"",children:"Select Class"}),["Class 6","Class 7","Class 8","Class 9","Class 10","Class 11","Class 12","Alumni"].map(e=>(0,i.jsx)("option",{value:e,children:e},e))]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"achievement_type",className:"block text-sm font-medium text-gray-700 mb-2",children:"Achievement Type *"}),(0,i.jsx)("select",{id:"achievement_type",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.achievement_type,onChange:e=>f({...p,achievement_type:e.target.value}),children:[{value:"academic",label:"Academic"},{value:"sports",label:"Sports"},{value:"cultural",label:"Cultural"},{value:"competition",label:"Competition"},{value:"scholarship",label:"Scholarship"},{value:"other",label:"Other"}].map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"achievement_date",className:"block text-sm font-medium text-gray-700 mb-2",children:"Achievement Date *"}),(0,i.jsx)("input",{type:"date",id:"achievement_date",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.achievement_date,onChange:e=>f({...p,achievement_date:e.target.value})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,i.jsxs)("select",{id:"status",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.status,onChange:e=>f({...p,status:e.target.value}),children:[(0,i.jsx)("option",{value:"new",children:"New"}),(0,i.jsx)("option",{value:"draft",children:"Draft"}),(0,i.jsx)("option",{value:"published",children:"Published"}),(0,i.jsx)("option",{value:"archived",children:"Archived"})]}),(0,i.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:(0,c.Ng)(p.status).description})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"award_by",className:"block text-sm font-medium text-gray-700 mb-2",children:"Awarded By"}),(0,i.jsx)("input",{type:"text",id:"award_by",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.award_by,onChange:e=>f({...p,award_by:e.target.value}),placeholder:"Organization or institution"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-2",children:"Position/Rank"}),(0,i.jsx)("input",{type:"text",id:"position",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.position,onChange:e=>f({...p,position:e.target.value}),placeholder:"e.g., 1st Place, AIR 100, Gold Medal"})]})]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",id:"is_published",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:p.is_published,onChange:e=>f({...p,is_published:e.target.checked})}),(0,i.jsx)("label",{htmlFor:"is_published",className:"ml-2 block text-sm text-gray-700",children:"Publish achievement"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,i.jsx)("button",{type:"button",onClick:()=>d.back(),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Cancel"}),(0,i.jsxs)("button",{type:"submit",disabled:u||g,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[g?"Uploading...":u?"Saving...":r?"Update":"Create"," Achievement"]})]})]})})}},8907:(e,t,r)=>{"use strict";r.d(t,{F9:()=>n,Ng:()=>l,qm:()=>s});let i={new:{label:"New",color:"text-blue-800",bgColor:"bg-blue-100",description:"Recently created content"},draft:{label:"Draft",color:"text-gray-800",bgColor:"bg-gray-100",description:"Work in progress, not published"},published:{label:"Published",color:"text-green-800",bgColor:"bg-green-100",description:"Live and visible to public"},archived:{label:"Archived",color:"text-yellow-800",bgColor:"bg-yellow-100",description:"Hidden from public, kept for reference"}};function l(e){return i[e]||i.draft}function s(e,t){let r=l(e),i="".concat(r.bgColor," ").concat(r.color);return"".concat("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"," ").concat(i," ").concat(t||"").trim()}function n(){return"new"}}}]);