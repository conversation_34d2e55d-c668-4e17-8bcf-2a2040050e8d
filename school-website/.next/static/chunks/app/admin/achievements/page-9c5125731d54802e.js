(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1859],{2900:(e,s,t)=>{Promise.resolve().then(t.bind(t,6384)),Promise.resolve().then(t.bind(t,6071))},5695:(e,s,t)=>{"use strict";var n=t(8999);t.o(n,"useRouter")&&t.d(s,{useRouter:function(){return n.useRouter}})},6071:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var n=t(5155),a=t(2115),r=t(6874),i=t.n(r),l=t(5695);function c(e){let{children:s,currentAdmin:t}=e,[r,c]=(0,a.useState)(!1),o=(0,l.useRouter)(),d=async()=>{try{await fetch("/api/admin/auth/logout",{method:"POST"}),o.push("/admin/login")}catch(e){console.error("Logout error:",e)}},m=[{name:"Dashboard",href:"/admin/dashboard",icon:"dashboard"},{name:"Announcements",href:"/admin/announcements",icon:"announcements"},{name:"Faculty",href:"/admin/faculty",icon:"faculty"},{name:"Gallery",href:"/admin/gallery",icon:"gallery"},{name:"Achievements",href:"/admin/achievements",icon:"trophy"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Users",href:"/admin/users",icon:"users",adminOnly:!0},{name:"Audit Logs",href:"/admin/audit",icon:"audit",adminOnly:!0},{name:"Backup",href:"/admin/backup",icon:"backup",adminOnly:!0},{name:"Settings",href:"/admin/settings",icon:"settings"},{name:"Achievements",href:"/admin/achievements",icon:"achievements"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Settings",href:"/admin/settings",icon:"settings"}],h=e=>{let s={dashboard:(0,n.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"})]}),announcements:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"})}),faculty:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),gallery:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),achievements:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),content:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),settings:(0,n.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})};return s[e]||s.dashboard};return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,n.jsxs)("div",{className:"fixed inset-0 flex z-40 md:hidden ".concat(r?"":"hidden"),children:[(0,n.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>c(!1)}),(0,n.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,n.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,n.jsx)("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>c(!1),children:(0,n.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})}),(0,n.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-green-600",children:"Admin Panel"})}),(0,n.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:m.filter(e=>!e.adminOnly||"super_admin"===t.role).map(e=>(0,n.jsxs)(i(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium rounded-md",children:[h(e.icon),(0,n.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})]})]}),(0,n.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,n.jsx)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:(0,n.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,n.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,n.jsx)("h1",{className:"text-xl font-bold text-green-600",children:"Admin Panel"})}),(0,n.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:m.filter(e=>!e.adminOnly||"super_admin"===t.role).map(e=>(0,n.jsxs)(i(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:[h(e.icon),(0,n.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})})}),(0,n.jsxs)("div",{className:"md:pl-64 flex flex-col flex-1",children:[(0,n.jsx)("div",{className:"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100",children:(0,n.jsx)("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500",onClick:()=>c(!0),children:(0,n.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,n.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Meritorious School Admin"})}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(i(),{href:"/",className:"text-gray-500 hover:text-gray-700 text-sm",target:"_blank",children:"View Website"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-700",children:t.full_name}),(0,n.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:t.role}),(0,n.jsx)("button",{onClick:d,className:"text-gray-500 hover:text-gray-700 text-sm",children:"Logout"})]})]})]})})}),(0,n.jsx)("main",{className:"flex-1",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:s})})]})]})}},6384:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var n=t(5155),a=t(2115),r=t(6874),i=t.n(r),l=t(6766),c=t(8907);function o(){let[e,s]=(0,a.useState)([]),[t,r]=(0,a.useState)(!0),[o,d]=(0,a.useState)("all"),[m,h]=(0,a.useState)("all");(0,a.useEffect)(()=>{x()},[]);let x=async()=>{try{let e=await fetch("/api/admin/achievements");if(e.ok){let t=await e.json();s(t)}}catch(e){console.error("Error fetching achievements:",e)}finally{r(!1)}},u=async t=>{if(confirm("Are you sure you want to delete this achievement?"))try{(await fetch("/api/admin/achievements/".concat(t),{method:"DELETE"})).ok&&s(e.filter(e=>e.id!==t))}catch(e){console.error("Error deleting achievement:",e)}},p=async(t,n)=>{try{(await fetch("/api/admin/achievements/".concat(t),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_published:!n})})).ok&&s(e.map(e=>e.id===t?{...e,is_published:!n}:e))}catch(e){console.error("Error updating achievement:",e)}},f=e.filter(e=>{let s="all"===o||"published"===o&&e.is_published||"draft"===o&&!e.is_published,t="all"===m||e.achievement_type===m;return s&&t});return t?(0,n.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,n.jsx)("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,n.jsxs)("div",{className:"flex space-x-4",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-gray-200 rounded"}),(0,n.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},s))})}):(0,n.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,n.jsx)("div",{className:"border-b border-gray-200",children:(0,n.jsx)("div",{className:"px-6 py-4",children:(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,n.jsx)("nav",{className:"flex space-x-8",children:[{key:"all",label:"All",count:e.length},{key:"published",label:"Published",count:e.filter(e=>e.is_published).length},{key:"draft",label:"Draft",count:e.filter(e=>!e.is_published).length}].map(e=>(0,n.jsxs)("button",{onClick:()=>d(e.key),className:"flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ".concat(o===e.key?"border-green-500 text-green-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,n.jsx)("span",{children:e.label}),(0,n.jsx)("span",{className:"bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs",children:e.count})]},e.key))}),(0,n.jsx)("select",{value:m,onChange:e=>h(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",children:["all","academic","sports","cultural","competition","scholarship","other"].map(e=>(0,n.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})})}),(0,n.jsx)("div",{className:"divide-y divide-gray-200",children:0===f.length?(0,n.jsx)("div",{className:"p-6 text-center text-gray-500",children:"No achievements found."}):f.map(e=>(0,n.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:e.image_url?(0,n.jsx)(l.default,{src:e.image_url,alt:e.title,width:64,height:64,className:"w-16 h-16 rounded object-cover"}):(0,n.jsx)("div",{className:"w-16 h-16 rounded bg-gray-200 flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})})})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 truncate",children:e.title}),(0,n.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"academic":return"bg-blue-100 text-blue-800";case"sports":return"bg-green-100 text-green-800";case"cultural":return"bg-purple-100 text-purple-800";case"competition":return"bg-orange-100 text-orange-800";case"scholarship":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(e.achievement_type)),children:e.achievement_type}),(0,n.jsx)("span",{className:(0,c.qm)(e.status,"text-xs"),children:e.status}),(0,n.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.is_published?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.is_published?"Published":"Draft"})]}),(0,n.jsxs)("div",{className:"mt-1 space-y-1",children:[e.student_name&&(0,n.jsxs)("p",{className:"text-sm font-medium text-gray-600",children:[e.student_name,e.student_class&&" - ".concat(e.student_class)]}),e.description&&(0,n.jsx)("p",{className:"text-sm text-gray-500 line-clamp-2",children:e.description}),(0,n.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,n.jsxs)("span",{children:["Date: ",new Date(e.achievement_date).toLocaleDateString()]}),e.award_by&&(0,n.jsxs)("span",{children:["By: ",e.award_by]}),e.position&&(0,n.jsxs)("span",{children:["Position: ",e.position]})]})]}),(0,n.jsxs)("div",{className:"mt-2 flex items-center text-sm text-gray-500 space-x-4",children:[(0,n.jsxs)("span",{children:["Created: ",new Date(e.created_at).toLocaleDateString()]}),(0,n.jsxs)("span",{children:["Updated: ",new Date(e.updated_at).toLocaleDateString()]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(i(),{href:"/admin/achievements/".concat(e.id,"/edit"),className:"text-green-600 hover:text-green-900 text-sm font-medium",children:"Edit"}),(0,n.jsx)("button",{onClick:()=>p(e.id,e.is_published),className:"text-blue-600 hover:text-blue-900 text-sm font-medium",children:e.is_published?"Unpublish":"Publish"}),(0,n.jsx)("button",{onClick:()=>u(e.id),className:"text-red-600 hover:text-red-900 text-sm font-medium",children:"Delete"})]})]})},e.id))})]})}},8907:(e,s,t)=>{"use strict";t.d(s,{F9:()=>i,Ng:()=>a,qm:()=>r});let n={new:{label:"New",color:"text-blue-800",bgColor:"bg-blue-100",description:"Recently created content"},draft:{label:"Draft",color:"text-gray-800",bgColor:"bg-gray-100",description:"Work in progress, not published"},published:{label:"Published",color:"text-green-800",bgColor:"bg-green-100",description:"Live and visible to public"},archived:{label:"Archived",color:"text-yellow-800",bgColor:"bg-yellow-100",description:"Hidden from public, kept for reference"}};function a(e){return n[e]||n.draft}function r(e,s){let t=a(e),n="".concat(t.bgColor," ").concat(t.color);return"".concat("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"," ").concat(n," ").concat(s||"").trim()}function i(){return"new"}}},e=>{e.O(0,[6874,6766,8441,5964,7358],()=>e(e.s=2900)),_N_E=e.O()}]);