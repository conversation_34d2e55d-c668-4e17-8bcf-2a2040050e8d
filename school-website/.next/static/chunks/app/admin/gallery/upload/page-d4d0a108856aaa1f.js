(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[645],{101:(e,a,s)=>{"use strict";s.d(a,{default:()=>o});var r=s(5155),l=s(2115),t=s(5695),i=s(6766),n=s(603);function o(){let e=(0,t.useRouter)(),[a,s]=(0,l.useState)([]),[o,d]=(0,l.useState)(!1),[c,u]=(0,l.useState)(!1),[m,g]=(0,l.useState)("events"),h=[{value:"events",label:"Events"},{value:"campus",label:"Campus"},{value:"sports",label:"Sports"},{value:"academics",label:"Academics"},{value:"achievements",label:"Achievements"}],x=(e,a,r)=>{s(s=>s.map((s,l)=>l===e?{...s,[a]:r}:s))},p=async()=>{if(0===a.length)return void alert("Please upload some images first");if(a.filter(e=>!e.title.trim()).length>0)return void alert("Please provide titles for all images");u(!0);try{let s=a.map(async e=>{let a=await fetch("/api/admin/gallery",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:e.title,description:e.description,image_url:e.url,category:e.category,status:"published",is_published:!0})});if(!a.ok)throw Error("Failed to save ".concat(e.title));return a.json()});await Promise.all(s),e.push("/admin/gallery")}catch(e){console.error("Error saving images:",e),alert("Error saving some images. Please try again.")}finally{u(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Upload Images"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"defaultCategory",className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Category"}),(0,r.jsx)("select",{id:"defaultCategory",value:m,onChange:e=>g(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",children:h.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(n.A,{bucket:"gallery",folder:"images",validationType:"image",multiple:!0,maxFiles:20,onUploadStart:()=>d(!0),onUploadComplete:e=>{d(!1);let a=e.filter(e=>e.success&&e.url).map(e=>({url:e.url,title:"",description:"",category:m}));s(e=>[...e,...a])}})]}),a.length>0&&(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("h2",{className:"text-lg font-medium text-gray-900",children:["Image Details (",a.length," images)"]}),(0,r.jsx)("button",{onClick:p,disabled:c||o,className:"px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 disabled:opacity-50",children:c?"Saving...":"Save All Images"})]}),(0,r.jsx)("div",{className:"space-y-6",children:a.map((e,a)=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(i.default,{src:e.url,alt:"Preview",width:96,height:96,className:"w-24 h-24 object-cover rounded-lg"})}),(0,r.jsxs)("div",{className:"flex-1 space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Title *"}),(0,r.jsx)("input",{type:"text",value:e.title,onChange:e=>x(a,"title",e.target.value),placeholder:"Enter image title",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),(0,r.jsx)("select",{value:e.category,onChange:e=>x(a,"category",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",children:h.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description (Optional)"}),(0,r.jsx)("textarea",{value:e.description,onChange:e=>x(a,"description",e.target.value),placeholder:"Enter image description",rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"})]})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("button",{onClick:()=>{s(e=>e.filter((e,s)=>s!==a))},className:"p-2 text-red-600 hover:text-red-800",title:"Remove image",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})})]})},a))})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("button",{onClick:()=>e.back(),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),a.length>0&&(0,r.jsx)("button",{onClick:p,disabled:c||o,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50",children:c?"Saving...":"Save ".concat(a.length," Images")})]})]})}},5695:(e,a,s)=>{"use strict";var r=s(8999);s.o(r,"useRouter")&&s.d(a,{useRouter:function(){return r.useRouter}})},8743:(e,a,s)=>{Promise.resolve().then(s.bind(s,6071)),Promise.resolve().then(s.bind(s,101))}},e=>{e.O(0,[6874,5647,6766,3893,8441,5964,7358],()=>e(e.s=8743)),_N_E=e.O()}]);