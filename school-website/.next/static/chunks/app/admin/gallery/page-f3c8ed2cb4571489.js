(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3831],{2767:(e,s,t)=>{"use strict";t.d(s,{default:()=>l});var r=t(5155),a=t(2115),n=t(6766),i=t(8907);function l(){let[e,s]=(0,a.useState)([]),[t,l]=(0,a.useState)(!0),[o,c]=(0,a.useState)("all"),[d,h]=(0,a.useState)([]),[m,x]=(0,a.useState)("grid");(0,a.useEffect)(()=>{u()},[]);let u=async()=>{try{let e=await fetch("/api/admin/gallery");if(e.ok){let t=await e.json();s(t)}}catch(e){console.error("Error fetching gallery items:",e)}finally{l(!1)}},g=async t=>{if(confirm("Are you sure you want to delete this image?"))try{(await fetch("/api/admin/gallery/".concat(t),{method:"DELETE"})).ok&&(s(e.filter(e=>e.id!==t)),h(d.filter(e=>e!==t)))}catch(e){console.error("Error deleting gallery item:",e)}},p=async e=>{if(0===d.length)return void alert("Please select items first");if("delete"!==e||confirm("Are you sure you want to delete ".concat(d.length," items?")))try{(await fetch("/api/admin/gallery/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:e,ids:d})})).ok&&(u(),h([]))}catch(e){console.error("Error performing bulk action:",e)}},f=()=>e.filter(e=>"all"===o||e.category===o),j=f();return t?(0,r.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,r.jsx)("div",{className:"animate-pulse space-y-4",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[...Array(8)].map((e,s)=>(0,r.jsx)("div",{className:"aspect-square bg-gray-200 rounded-lg"},s))})})}):(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("select",{value:o,onChange:e=>c(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",children:["all","events","campus","sports","academics","achievements"].map(e=>(0,r.jsx)("option",{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[j.length," items"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>x("grid"),className:"p-2 rounded ".concat("grid"===m?"bg-green-100 text-green-600":"text-gray-400"),children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})})}),(0,r.jsx)("button",{onClick:()=>x("list"),className:"p-2 rounded ".concat("list"===m?"bg-green-100 text-green-600":"text-gray-400"),children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 10h16M4 14h16M4 18h16"})})})]})]}),d.length>0&&(0,r.jsxs)("div",{className:"mt-4 flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[d.length," selected"]}),(0,r.jsx)("button",{onClick:()=>p("publish"),className:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200",children:"Publish"}),(0,r.jsx)("button",{onClick:()=>p("unpublish"),className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200",children:"Unpublish"}),(0,r.jsx)("button",{onClick:()=>p("delete"),className:"px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200",children:"Delete"})]})]}),(0,r.jsx)("div",{className:"p-6",children:0===j.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,r.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No images"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Get started by uploading some images."})]}):"grid"===m?(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:j.map(e=>(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsxs)("div",{className:"aspect-square bg-gray-200 rounded-lg overflow-hidden",children:[(0,r.jsx)(n.default,{src:e.image_url,alt:e.title,width:300,height:300,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity duration-200 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{d.includes(e.id)?h(d.filter(s=>s!==e.id)):h([...d,e.id])},className:"p-2 rounded-full ".concat(d.includes(e.id)?"bg-green-600 text-white":"bg-white text-gray-700"),children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsx)("button",{onClick:()=>g(e.id),className:"p-2 bg-red-600 text-white rounded-full hover:bg-red-700",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})})]}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-1",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:e.category}),(0,r.jsx)("span",{className:(0,i.qm)(e.status,"text-xs"),children:e.status})]})]})]},e.id))}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm font-medium text-gray-500 border-b pb-2",children:[(0,r.jsx)("input",{type:"checkbox",checked:d.length===j.length&&j.length>0,onChange:()=>{let e=f();d.length===e.length?h([]):h(e.map(e=>e.id))},className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"}),(0,r.jsx)("span",{className:"flex-1",children:"Title"}),(0,r.jsx)("span",{className:"w-20",children:"Category"}),(0,r.jsx)("span",{className:"w-20",children:"Status"}),(0,r.jsx)("span",{className:"w-24",children:"Actions"})]}),j.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-4 py-2 border-b border-gray-100",children:[(0,r.jsx)("input",{type:"checkbox",checked:d.includes(e.id),onChange:s=>{s.target.checked?h([...d,e.id]):h(d.filter(s=>s!==e.id))},className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 flex-1",children:[(0,r.jsx)(n.default,{src:e.image_url,alt:e.title,width:48,height:48,className:"w-12 h-12 object-cover rounded"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:e.title}),e.description&&(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.description})]})]}),(0,r.jsx)("span",{className:"w-20 text-sm text-gray-500",children:e.category}),(0,r.jsx)("span",{className:"w-20 ".concat((0,i.qm)(e.status,"text-xs")),children:e.status}),(0,r.jsx)("div",{className:"w-24 flex space-x-2",children:(0,r.jsx)("button",{onClick:()=>g(e.id),className:"text-red-600 hover:text-red-800 text-sm",children:"Delete"})})]},e.id))]})})]})}},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})},6055:(e,s,t)=>{Promise.resolve().then(t.bind(t,6071)),Promise.resolve().then(t.bind(t,2767))},6071:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var r=t(5155),a=t(2115),n=t(6874),i=t.n(n),l=t(5695);function o(e){let{children:s,currentAdmin:t}=e,[n,o]=(0,a.useState)(!1),c=(0,l.useRouter)(),d=async()=>{try{await fetch("/api/admin/auth/logout",{method:"POST"}),c.push("/admin/login")}catch(e){console.error("Logout error:",e)}},h=[{name:"Dashboard",href:"/admin/dashboard",icon:"dashboard"},{name:"Announcements",href:"/admin/announcements",icon:"announcements"},{name:"Faculty",href:"/admin/faculty",icon:"faculty"},{name:"Gallery",href:"/admin/gallery",icon:"gallery"},{name:"Achievements",href:"/admin/achievements",icon:"trophy"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Users",href:"/admin/users",icon:"users",adminOnly:!0},{name:"Audit Logs",href:"/admin/audit",icon:"audit",adminOnly:!0},{name:"Backup",href:"/admin/backup",icon:"backup",adminOnly:!0},{name:"Settings",href:"/admin/settings",icon:"settings"},{name:"Achievements",href:"/admin/achievements",icon:"achievements"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Settings",href:"/admin/settings",icon:"settings"}],m=e=>{let s={dashboard:(0,r.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"})]}),announcements:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"})}),faculty:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),gallery:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),achievements:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),content:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),settings:(0,r.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})};return s[e]||s.dashboard};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsxs)("div",{className:"fixed inset-0 flex z-40 md:hidden ".concat(n?"":"hidden"),children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>o(!1)}),(0,r.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,r.jsx)("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>o(!1),children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})}),(0,r.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-green-600",children:"Admin Panel"})}),(0,r.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:h.filter(e=>!e.adminOnly||"super_admin"===t.role).map(e=>(0,r.jsxs)(i(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium rounded-md",children:[m(e.icon),(0,r.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})]})]}),(0,r.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,r.jsx)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:(0,r.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,r.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-green-600",children:"Admin Panel"})}),(0,r.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:h.filter(e=>!e.adminOnly||"super_admin"===t.role).map(e=>(0,r.jsxs)(i(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:[m(e.icon),(0,r.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})})}),(0,r.jsxs)("div",{className:"md:pl-64 flex flex-col flex-1",children:[(0,r.jsx)("div",{className:"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100",children:(0,r.jsx)("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500",onClick:()=>o(!0),children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,r.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Meritorious School Admin"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i(),{href:"/",className:"text-gray-500 hover:text-gray-700 text-sm",target:"_blank",children:"View Website"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700",children:t.name}),(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:t.role}),(0,r.jsx)("button",{onClick:d,className:"text-gray-500 hover:text-gray-700 text-sm",children:"Logout"})]})]})]})})}),(0,r.jsx)("main",{className:"flex-1",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:s})})]})]})}},8907:(e,s,t)=>{"use strict";t.d(s,{F9:()=>i,Ng:()=>a,qm:()=>n});let r={new:{label:"New",color:"text-blue-800",bgColor:"bg-blue-100",description:"Recently created content"},draft:{label:"Draft",color:"text-gray-800",bgColor:"bg-gray-100",description:"Work in progress, not published"},published:{label:"Published",color:"text-green-800",bgColor:"bg-green-100",description:"Live and visible to public"},archived:{label:"Archived",color:"text-yellow-800",bgColor:"bg-yellow-100",description:"Hidden from public, kept for reference"}};function a(e){return r[e]||r.draft}function n(e,s){let t=a(e),r="".concat(t.bgColor," ").concat(t.color);return"".concat("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"," ").concat(r," ").concat(s||"").trim()}function i(){return"new"}}},e=>{e.O(0,[6874,6766,8441,5964,7358],()=>e(e.s=6055)),_N_E=e.O()}]);