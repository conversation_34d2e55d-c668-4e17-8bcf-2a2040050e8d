(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6912],{5981:(e,a,r)=>{Promise.resolve().then(r.bind(r,7434))},7434:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>n});var s=r(5155),t=r(2115);function n(){let[e,a]=(0,t.useState)(!1),[r,n]=(0,t.useState)(""),[i,l]=(0,t.useState)(""),[d,o]=(0,t.useState)(!1),c=async()=>{a(!0),l(""),n("");try{let e=await fetch("/api/admin/init-db",{method:"POST"}),a=await e.json();e.ok&&a.success?(n("Database initialized successfully!"),a.adminUser&&(o(!0),n("Database initialized! Admin user created with email: ".concat(a.adminUser.email)))):l(a.error||"Failed to initialize database")}catch(e){l("Network error. Please try again.")}finally{a(!1)}},m=async e=>{e.preventDefault(),a(!0),l(""),n("");let r=new FormData(e.currentTarget),s=r.get("email"),t=r.get("password"),i=r.get("name");try{let e=await fetch("/api/admin/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:s,password:t,name:i,role:"super_admin"})}),a=await e.json();e.ok?(n("Super admin user created successfully! You can now login with email: ".concat(s)),o(!0)):l(a.error||"Failed to create admin user")}catch(e){l("Network error. Please try again.")}finally{a(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"School Website Setup"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Initialize your school website database and create admin user"})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[r&&(0,s.jsx)("div",{className:"mb-4 bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm",children:r}),i&&(0,s.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:i}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Step 1: Initialize Database"}),(0,s.jsx)("button",{onClick:c,disabled:e,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:e?"Initializing...":"Initialize Database"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"This will create all necessary database tables and initial data."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Step 2: Create Super Admin User"}),d?(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-green-600 mb-4",children:(0,s.jsx)("svg",{className:"mx-auto h-12 w-12",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Setup completed successfully! You can now access the admin panel."}),(0,s.jsx)("a",{href:"/admin/login",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700",children:"Go to Admin Login"})]}):(0,s.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,s.jsx)("input",{id:"name",name:"name",type:"text",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm",placeholder:"Enter your full name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",required:!0,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm",placeholder:"Enter your email address"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",required:!0,minLength:8,className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm",placeholder:"Enter a secure password (min 8 characters)"})]}),(0,s.jsx)("button",{type:"submit",disabled:e,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:e?"Creating...":"Create Super Admin"})]})]}),(0,s.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Important Notes:"}),(0,s.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsx)("li",{children:"• Make sure your Supabase environment variables are configured"}),(0,s.jsx)("li",{children:"• The super admin user will have full access to all features"}),(0,s.jsx)("li",{children:"• You can create additional admin users from the admin panel"}),(0,s.jsx)("li",{children:"• Keep your admin credentials secure"})]})]})]})})]})}}},e=>{e.O(0,[8441,5964,7358],()=>e(e.s=5981)),_N_E=e.O()}]);