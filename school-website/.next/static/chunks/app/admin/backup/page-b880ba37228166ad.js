(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[49],{2221:(e,s,n)=>{Promise.resolve().then(n.bind(n,6071)),Promise.resolve().then(n.bind(n,6311))},5695:(e,s,n)=>{"use strict";var r=n(8999);n.o(r,"useRouter")&&n.d(s,{useRouter:function(){return r.useRouter}})},6071:(e,s,n)=>{"use strict";n.d(s,{default:()=>l});var r=n(5155),t=n(2115),a=n(6874),i=n.n(a),o=n(5695);function l(e){let{children:s,currentAdmin:n}=e,[a,l]=(0,t.useState)(!1),c=(0,o.useRouter)(),d=async()=>{try{await fetch("/api/admin/auth/logout",{method:"POST"}),c.push("/admin/login")}catch(e){console.error("Logout error:",e)}},m=[{name:"Dashboard",href:"/admin/dashboard",icon:"dashboard"},{name:"Announcements",href:"/admin/announcements",icon:"announcements"},{name:"Faculty",href:"/admin/faculty",icon:"faculty"},{name:"Gallery",href:"/admin/gallery",icon:"gallery"},{name:"Achievements",href:"/admin/achievements",icon:"trophy"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Users",href:"/admin/users",icon:"users",adminOnly:!0},{name:"Audit Logs",href:"/admin/audit",icon:"audit",adminOnly:!0},{name:"Backup",href:"/admin/backup",icon:"backup",adminOnly:!0},{name:"Settings",href:"/admin/settings",icon:"settings"},{name:"Achievements",href:"/admin/achievements",icon:"achievements"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Settings",href:"/admin/settings",icon:"settings"}],x=e=>{let s={dashboard:(0,r.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"})]}),announcements:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"})}),faculty:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),gallery:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),achievements:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),content:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),settings:(0,r.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})};return s[e]||s.dashboard};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsxs)("div",{className:"fixed inset-0 flex z-40 md:hidden ".concat(a?"":"hidden"),children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>l(!1)}),(0,r.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,r.jsx)("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>l(!1),children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})}),(0,r.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,r.jsx)("h1",{className:"text-lg font-semibold text-green-600",children:"Admin Panel"})}),(0,r.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:m.filter(e=>!e.adminOnly||"super_admin"===n.role).map(e=>(0,r.jsxs)(i(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium rounded-md",children:[x(e.icon),(0,r.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})]})]}),(0,r.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,r.jsx)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:(0,r.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,r.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-green-600",children:"Admin Panel"})}),(0,r.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:m.filter(e=>!e.adminOnly||"super_admin"===n.role).map(e=>(0,r.jsxs)(i(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:[x(e.icon),(0,r.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})})}),(0,r.jsxs)("div",{className:"md:pl-64 flex flex-col flex-1",children:[(0,r.jsx)("div",{className:"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100",children:(0,r.jsx)("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500",onClick:()=>l(!0),children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,r.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Meritorious School Admin"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(i(),{href:"/",className:"text-gray-500 hover:text-gray-700 text-sm",target:"_blank",children:"View Website"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-700",children:n.full_name}),(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:n.role}),(0,r.jsx)("button",{onClick:d,className:"text-gray-500 hover:text-gray-700 text-sm",children:"Logout"})]})]})]})})}),(0,r.jsx)("main",{className:"flex-1",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:s})})]})]})}},6311:(e,s,n)=>{"use strict";n.d(s,{default:()=>a});var r=n(5155),t=n(2115);function a(){let[e,s]=(0,t.useState)(!1),[n,a]=(0,t.useState)(""),[i,o]=(0,t.useState)(""),l=async e=>{s(!0),o(""),a("");try{let s=await fetch("/api/admin/export?type=".concat(e),{method:"GET"});if(s.ok){var n;let r=s.headers.get("content-disposition"),t=r?null==(n=r.split("filename=")[1])?void 0:n.replace(/"/g,""):"".concat(e,"-export-").concat(new Date().toISOString().split("T")[0],".json"),i=await s.blob(),o=window.URL.createObjectURL(i),l=document.createElement("a");l.href=o,l.download=t,document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(o),document.body.removeChild(l),a("".concat("all"===e?"Complete backup":e," exported successfully!"))}else{let e=await s.json();o(e.error||"Export failed")}}catch(e){o("Export failed. Please try again.")}finally{s(!1)}},c=async()=>{s(!0),o(""),a("");try{let e=await fetch("/api/admin/backup",{method:"POST"}),s=await e.json();e.ok?a("Database backup created successfully!"):o(s.error||"Backup failed")}catch(e){o("Backup failed. Please try again.")}finally{s(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[n&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm",children:n}),i&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:i}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("button",{onClick:()=>l("all"),disabled:e,className:"flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),e?"Exporting...":"Export Complete Backup"]}),(0,r.jsxs)("button",{onClick:c,disabled:e,className:"flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})}),e?"Creating...":"Create Database Backup"]})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Individual Data Exports"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{key:"announcements",label:"Announcements",description:"Export all announcements with content and metadata"},{key:"faculty",label:"Faculty",description:"Export faculty profiles and information"},{key:"achievements",label:"Achievements",description:"Export student achievements and awards"},{key:"gallery",label:"Gallery",description:"Export gallery images and metadata"},{key:"contacts",label:"Contact Submissions",description:"Export contact form submissions"},{key:"settings",label:"School Settings",description:"Export school configuration and settings"},{key:"all",label:"Complete Backup",description:"Export all data in a comprehensive backup"}].filter(e=>"all"!==e.key).map(s=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:s.label}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:s.description}),(0,r.jsxs)("button",{onClick:()=>l(s.key),disabled:e,className:"w-full flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Export"]})]},s.key))})]}),(0,r.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Backup Information"}),(0,r.jsx)("div",{className:"prose prose-sm text-gray-600",children:(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Export Format:"})," Data is exported in JSON format for easy import/restore"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"File Handling:"})," Images and files are referenced by URL in exports"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Security:"})," Sensitive data like passwords are excluded from exports"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Frequency:"})," Regular backups are recommended weekly or before major changes"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Storage:"})," Store backups securely and in multiple locations"]})]})}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-yellow-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Important Note"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"Backup and export operations are only available to Super Admin users. Always verify backup integrity before relying on them for recovery."})]})]})})]})]})}}},e=>{e.O(0,[6874,8441,5964,7358],()=>e(e.s=2221)),_N_E=e.O()}]);