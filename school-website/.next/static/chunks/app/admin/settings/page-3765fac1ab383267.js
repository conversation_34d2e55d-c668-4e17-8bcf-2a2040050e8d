(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7122],{4241:(e,s,o)=>{Promise.resolve().then(o.bind(o,6071)),Promise.resolve().then(o.bind(o,4561))},4561:(e,s,o)=>{"use strict";o.d(s,{default:()=>i});var l=o(5155),r=o(2115),a=o(6766),t=o(603);function i(){let[e,s]=(0,r.useState)({}),[o,i]=(0,r.useState)(!0),[n,c]=(0,r.useState)(!1),[d,u]=(0,r.useState)(!1),[m,h]=(0,r.useState)(""),[g,p]=(0,r.useState)("");(0,r.useEffect)(()=>{b()},[]);let b=async()=>{try{let e=await fetch("/api/admin/settings");if(e.ok){let o=await e.json(),l={};Object.keys(o).forEach(e=>{l[e]=o[e].value}),s(l)}}catch(e){console.error("Error fetching settings:",e),h("Failed to load settings")}finally{i(!1)}},x=async s=>{s.preventDefault(),c(!0),h(""),p("");try{let s={settings:{school_logo:{value:e.school_logo||"",type:"image",description:"School logo image",is_public:!0},school_name:{value:e.school_name||"",type:"text",description:"Official school name",is_public:!0},school_tagline:{value:e.school_tagline||"",type:"text",description:"School tagline or motto",is_public:!0},school_address:{value:e.school_address||"",type:"text",description:"School physical address",is_public:!0},school_phone:{value:e.school_phone||"",type:"text",description:"School contact phone number",is_public:!0},school_email:{value:e.school_email||"",type:"text",description:"School contact email",is_public:!0},school_website:{value:e.school_website||"",type:"url",description:"School website URL",is_public:!0},principal_name:{value:e.principal_name||"",type:"text",description:"Principal name",is_public:!0},principal_message:{value:e.principal_message||"",type:"text",description:"Principal welcome message",is_public:!0},established_year:{value:e.established_year||"",type:"text",description:"Year school was established",is_public:!0}}},o=await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),l=await o.json();o.ok?(p("Settings updated successfully!"),setTimeout(()=>p(""),3e3)):h(l.error||"Failed to update settings")}catch(e){h("An error occurred. Please try again.")}finally{c(!1)}},f=(e,o)=>{s(s=>({...s,[e]:o}))};return o?(0,l.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,l.jsx)("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,l.jsx)("div",{className:"h-10 bg-gray-200 rounded"})]},s))})}):(0,l.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,l.jsxs)("form",{onSubmit:x,className:"space-y-6 p-6",children:[m&&(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:m}),g&&(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm",children:g}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"School Logo"}),e.school_logo&&(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)(a.default,{src:e.school_logo,alt:"School Logo",width:120,height:120,className:"w-30 h-30 object-contain rounded border"})}),(0,l.jsx)(t.A,{bucket:"school",folder:"branding",validationType:"image",multiple:!1,onUploadStart:()=>u(!0),onUploadComplete:e=>{u(!1);let s=e.find(e=>e.success&&e.url);s&&f("school_logo",s.url)},className:"mb-4"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"school_name",className:"block text-sm font-medium text-gray-700 mb-2",children:"School Name"}),(0,l.jsx)("input",{type:"text",id:"school_name",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.school_name||"",onChange:e=>f("school_name",e.target.value),placeholder:"Enter school name"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"school_tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:"School Tagline"}),(0,l.jsx)("input",{type:"text",id:"school_tagline",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.school_tagline||"",onChange:e=>f("school_tagline",e.target.value),placeholder:"Enter school tagline or motto"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"school_address",className:"block text-sm font-medium text-gray-700 mb-2",children:"School Address"}),(0,l.jsx)("textarea",{id:"school_address",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.school_address||"",onChange:e=>f("school_address",e.target.value),placeholder:"Enter complete school address"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"school_phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,l.jsx)("input",{type:"tel",id:"school_phone",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.school_phone||"",onChange:e=>f("school_phone",e.target.value),placeholder:"Enter phone number"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"school_email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,l.jsx)("input",{type:"email",id:"school_email",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.school_email||"",onChange:e=>f("school_email",e.target.value),placeholder:"Enter email address"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"school_website",className:"block text-sm font-medium text-gray-700 mb-2",children:"Website URL"}),(0,l.jsx)("input",{type:"url",id:"school_website",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.school_website||"",onChange:e=>f("school_website",e.target.value),placeholder:"Enter website URL"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"principal_name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Principal Name"}),(0,l.jsx)("input",{type:"text",id:"principal_name",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.principal_name||"",onChange:e=>f("principal_name",e.target.value),placeholder:"Enter principal name"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"established_year",className:"block text-sm font-medium text-gray-700 mb-2",children:"Established Year"}),(0,l.jsx)("input",{type:"text",id:"established_year",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.established_year||"",onChange:e=>f("established_year",e.target.value),placeholder:"Enter year established"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"principal_message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Principal Message"}),(0,l.jsx)("textarea",{id:"principal_message",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.principal_message||"",onChange:e=>f("principal_message",e.target.value),placeholder:"Enter principal welcome message"})]}),(0,l.jsx)("div",{className:"flex items-center justify-end pt-6 border-t border-gray-200",children:(0,l.jsx)("button",{type:"submit",disabled:n||d,className:"px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:d?"Uploading...":n?"Saving...":"Save Settings"})})]})})}},5695:(e,s,o)=>{"use strict";var l=o(8999);o.o(l,"useRouter")&&o.d(s,{useRouter:function(){return l.useRouter}})}},e=>{e.O(0,[6874,5647,6766,3893,8441,5964,7358],()=>e(e.s=4241)),_N_E=e.O()}]);