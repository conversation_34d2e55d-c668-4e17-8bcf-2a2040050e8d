(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4530],{384:(e,s,a)=>{"use strict";a.d(s,{default:()=>r});var n=a(5155),t=a(2115);function r(){let[e,s]=(0,t.useState)([]),[a,r]=(0,t.useState)(!0),[i,l]=(0,t.useState)("all"),[c,d]=(0,t.useState)(1),[o,m]=(0,t.useState)(1);(0,t.useEffect)(()=>{x()},[i,c]);let x=async()=>{try{let e=new URLSearchParams({page:c.toString(),limit:"20",..."all"!==i&&{action:i}}),a=await fetch("/api/admin/audit?".concat(e));if(a.ok){let e=await a.json();s(e.logs),m(e.pagination.totalPages)}}catch(e){console.error("Error fetching audit logs:",e)}finally{r(!1)}},h=e=>e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,e=>e.toUpperCase());return a?(0,n.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,n.jsx)("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,n.jsxs)("div",{className:"flex space-x-4",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded"}),(0,n.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3"}),(0,n.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},s))})}):(0,n.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,n.jsx)("div",{className:"border-b border-gray-200 p-4",children:(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"action-filter",className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Action"}),(0,n.jsx)("select",{id:"action-filter",value:i,onChange:e=>{l(e.target.value),d(1)},className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",children:["all","CREATE_ANNOUNCEMENT","UPDATE_ANNOUNCEMENT","DELETE_ANNOUNCEMENT","CREATE_FACULTY","UPDATE_FACULTY","DELETE_FACULTY","CREATE_ACHIEVEMENT","UPDATE_ACHIEVEMENT","DELETE_ACHIEVEMENT","CREATE_ADMIN_USER","UPDATE_ADMIN_USER","DELETE_ADMIN_USER","LOGIN","LOGOUT"].map(e=>(0,n.jsx)("option",{value:e,children:"all"===e?"All Actions":h(e)},e))})]}),(0,n.jsxs)("div",{className:"text-sm text-gray-500",children:["Total logs: ",e.length>0?"".concat((c-1)*20+1,"-").concat(Math.min(20*c,e.length)," of many"):"0"]})]})}),(0,n.jsx)("div",{className:"divide-y divide-gray-200",children:0===e.length?(0,n.jsx)("div",{className:"p-6 text-center text-gray-500",children:"No audit logs found."}):e.map(e=>{var s,a,t,r;return(0,n.jsx)("div",{className:"p-4 hover:bg-gray-50",children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("span",{className:"text-lg",children:(t=e.action).includes("CREATE")?"➕":t.includes("UPDATE")?"✏️":t.includes("DELETE")?"\uD83D\uDDD1️":t.includes("LOGIN")?"\uD83D\uDD10":t.includes("LOGOUT")?"\uD83D\uDEAA":"\uD83D\uDCDD"}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,n.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((r=e.action).includes("CREATE")?"bg-green-100 text-green-800":r.includes("UPDATE")?"bg-blue-100 text-blue-800":r.includes("DELETE")?"bg-red-100 text-red-800":r.includes("LOGIN")||r.includes("LOGOUT")?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:h(e.action)}),(0,n.jsxs)("span",{className:"text-sm text-gray-600",children:["on ",e.table_name]}),e.record_id&&(0,n.jsxs)("span",{className:"text-xs text-gray-500 font-mono",children:["ID: ",e.record_id.substring(0,8),"..."]})]}),(0,n.jsxs)("div",{className:"text-sm text-gray-900 mb-1",children:[(0,n.jsx)("span",{className:"font-medium",children:(null==(s=e.admin_users)?void 0:s.name)||"Unknown Admin"}),(0,n.jsxs)("span",{className:"text-gray-500 ml-2",children:["(",(null==(a=e.admin_users)?void 0:a.email)||"<EMAIL>",")"]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[(0,n.jsx)("span",{children:new Date(e.created_at).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"})}),e.ip_address&&(0,n.jsxs)("span",{children:["IP: ",e.ip_address]})]}),e.action.includes("UPDATE")&&e.old_values&&e.new_values&&(0,n.jsxs)("details",{className:"mt-2",children:[(0,n.jsx)("summary",{className:"text-xs text-blue-600 cursor-pointer hover:text-blue-800",children:"View Changes"}),(0,n.jsx)("div",{className:"mt-2 p-2 bg-gray-50 rounded text-xs",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Before:"}),(0,n.jsx)("pre",{className:"mt-1 text-xs text-gray-600 whitespace-pre-wrap",children:JSON.stringify(e.old_values,null,2)})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"After:"}),(0,n.jsx)("pre",{className:"mt-1 text-xs text-gray-600 whitespace-pre-wrap",children:JSON.stringify(e.new_values,null,2)})]})]})})]})]})]})},e.id)})}),o>1&&(0,n.jsxs)("div",{className:"border-t border-gray-200 px-4 py-3 flex items-center justify-between",children:[(0,n.jsx)("button",{onClick:()=>d(Math.max(1,c-1)),disabled:1===c,className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,n.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",c," of ",o]}),(0,n.jsx)("button",{onClick:()=>d(Math.min(o,c+1)),disabled:c===o,className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})}},5302:(e,s,a)=>{Promise.resolve().then(a.bind(a,6071)),Promise.resolve().then(a.bind(a,384))},5695:(e,s,a)=>{"use strict";var n=a(8999);a.o(n,"useRouter")&&a.d(s,{useRouter:function(){return n.useRouter}})},6071:(e,s,a)=>{"use strict";a.d(s,{default:()=>c});var n=a(5155),t=a(2115),r=a(6874),i=a.n(r),l=a(5695);function c(e){let{children:s,currentAdmin:a}=e,[r,c]=(0,t.useState)(!1),d=(0,l.useRouter)(),o=async()=>{try{await fetch("/api/admin/auth/logout",{method:"POST"}),d.push("/admin/login")}catch(e){console.error("Logout error:",e)}},m=[{name:"Dashboard",href:"/admin/dashboard",icon:"dashboard"},{name:"Announcements",href:"/admin/announcements",icon:"announcements"},{name:"Faculty",href:"/admin/faculty",icon:"faculty"},{name:"Gallery",href:"/admin/gallery",icon:"gallery"},{name:"Achievements",href:"/admin/achievements",icon:"trophy"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Users",href:"/admin/users",icon:"users",adminOnly:!0},{name:"Audit Logs",href:"/admin/audit",icon:"audit",adminOnly:!0},{name:"Backup",href:"/admin/backup",icon:"backup",adminOnly:!0},{name:"Settings",href:"/admin/settings",icon:"settings"},{name:"Achievements",href:"/admin/achievements",icon:"achievements"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Settings",href:"/admin/settings",icon:"settings"}],x=e=>{let s={dashboard:(0,n.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"})]}),announcements:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"})}),faculty:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),gallery:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),achievements:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),content:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),settings:(0,n.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})};return s[e]||s.dashboard};return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,n.jsxs)("div",{className:"fixed inset-0 flex z-40 md:hidden ".concat(r?"":"hidden"),children:[(0,n.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>c(!1)}),(0,n.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,n.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,n.jsx)("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>c(!1),children:(0,n.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})}),(0,n.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-green-600",children:"Admin Panel"})}),(0,n.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:m.filter(e=>!e.adminOnly||"super_admin"===a.role).map(e=>(0,n.jsxs)(i(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium rounded-md",children:[x(e.icon),(0,n.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})]})]}),(0,n.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,n.jsx)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:(0,n.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,n.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,n.jsx)("h1",{className:"text-xl font-bold text-green-600",children:"Admin Panel"})}),(0,n.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:m.filter(e=>!e.adminOnly||"super_admin"===a.role).map(e=>(0,n.jsxs)(i(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:[x(e.icon),(0,n.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})})}),(0,n.jsxs)("div",{className:"md:pl-64 flex flex-col flex-1",children:[(0,n.jsx)("div",{className:"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100",children:(0,n.jsx)("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500",onClick:()=>c(!0),children:(0,n.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,n.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Meritorious School Admin"})}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(i(),{href:"/",className:"text-gray-500 hover:text-gray-700 text-sm",target:"_blank",children:"View Website"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-700",children:a.full_name}),(0,n.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:a.role}),(0,n.jsx)("button",{onClick:o,className:"text-gray-500 hover:text-gray-700 text-sm",children:"Logout"})]})]})]})})}),(0,n.jsx)("main",{className:"flex-1",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:s})})]})]})}}},e=>{e.O(0,[6874,8441,5964,7358],()=>e(e.s=5302)),_N_E=e.O()}]);