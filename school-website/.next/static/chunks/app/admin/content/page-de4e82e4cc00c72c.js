(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9556],{1280:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var n=t(5155),i=t(5109),r=t(1652),a=t(1891),l=t(6761),o=t(4109),c=t(4589),d=t(6377),h=t(2115);function m(e){let{content:s,onChange:t,placeholder:m="Start writing...",className:x=""}=e,[u,g]=(0,h.useState)(!1),[f,p]=(0,h.useState)(""),v=(0,i.hG)({extensions:[r.A,a.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),l.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-green-600 underline hover:text-green-700"}}),o.A.configure({types:["heading","paragraph"]}),c.A,d.xJ],content:s,onUpdate:e=>{let{editor:s}=e;t(s.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}}});if(!v)return null;let b=e=>{let{onClick:s,isActive:t=!1,disabled:i=!1,children:r,title:a}=e;return(0,n.jsx)("button",{type:"button",onClick:s,disabled:i,title:a,className:"p-2 rounded text-sm font-medium transition-colors ".concat(t?"bg-green-100 text-green-700 border border-green-300":"text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent"," ").concat(i?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:r})};return(0,n.jsxs)("div",{className:"border border-gray-300 rounded-lg overflow-hidden ".concat(x),children:[(0,n.jsxs)("div",{className:"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleBold().run(),isActive:v.isActive("bold"),title:"Bold",children:(0,n.jsx)("strong",{children:"B"})}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleItalic().run(),isActive:v.isActive("italic"),title:"Italic",children:(0,n.jsx)("em",{children:"I"})}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleStrike().run(),isActive:v.isActive("strike"),title:"Strikethrough",children:(0,n.jsx)("s",{children:"S"})})]}),(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleHeading({level:1}).run(),isActive:v.isActive("heading",{level:1}),title:"Heading 1",children:"H1"}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleHeading({level:2}).run(),isActive:v.isActive("heading",{level:2}),title:"Heading 2",children:"H2"}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleHeading({level:3}).run(),isActive:v.isActive("heading",{level:3}),title:"Heading 3",children:"H3"})]}),(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleBulletList().run(),isActive:v.isActive("bulletList"),title:"Bullet List",children:"•"}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleOrderedList().run(),isActive:v.isActive("orderedList"),title:"Numbered List",children:"1."})]}),(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(b,{onClick:()=>v.chain().focus().setTextAlign("left").run(),isActive:v.isActive({textAlign:"left"}),title:"Align Left",children:"⬅"}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().setTextAlign("center").run(),isActive:v.isActive({textAlign:"center"}),title:"Align Center",children:"↔"}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().setTextAlign("right").run(),isActive:v.isActive({textAlign:"right"}),title:"Align Right",children:"➡"})]}),(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(b,{onClick:()=>{p(v.getAttributes("link").href||""),g(!0)},isActive:v.isActive("link"),title:"Add Link",children:"\uD83D\uDD17"}),(0,n.jsx)(b,{onClick:()=>{let e=window.prompt("Enter image URL:");e&&v.chain().focus().setImage({src:e}).run()},title:"Add Image",children:"\uD83D\uDDBC"})]}),(0,n.jsxs)("div",{className:"flex gap-1",children:[(0,n.jsx)(b,{onClick:()=>v.chain().focus().toggleBlockquote().run(),isActive:v.isActive("blockquote"),title:"Quote",children:'"'}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().setHorizontalRule().run(),title:"Horizontal Rule",children:"―"}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().undo().run(),disabled:!v.can().chain().focus().undo().run(),title:"Undo",children:"↶"}),(0,n.jsx)(b,{onClick:()=>v.chain().focus().redo().run(),disabled:!v.can().chain().focus().redo().run(),title:"Redo",children:"↷"})]})]}),(0,n.jsxs)("div",{className:"min-h-[200px] bg-white",children:[(0,n.jsx)(i.$Z,{editor:v,className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"}),v.isEmpty&&(0,n.jsx)("div",{className:"absolute top-16 left-4 text-gray-400 pointer-events-none",children:m})]}),u&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add Link"}),(0,n.jsx)("input",{type:"url",value:f,onChange:e=>p(e.target.value),placeholder:"Enter URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 mb-4",autoFocus:!0}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{type:"button",onClick:()=>g(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,n.jsx)("button",{type:"button",onClick:()=>{""===f?v.chain().focus().extendMarkRange("link").unsetLink().run():v.chain().focus().extendMarkRange("link").setLink({href:f}).run(),g(!1),p("")},className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700",children:"Add Link"})]})]})})]})}},2214:(e,s,t)=>{Promise.resolve().then(t.bind(t,6071)),Promise.resolve().then(t.bind(t,9340))},6071:(e,s,t)=>{"use strict";t.d(s,{default:()=>o});var n=t(5155),i=t(2115),r=t(6874),a=t.n(r),l=t(5695);function o(e){let{children:s,currentAdmin:t}=e,[r,o]=(0,i.useState)(!1),c=(0,l.useRouter)(),d=async()=>{try{await fetch("/api/admin/auth/logout",{method:"POST"}),c.push("/admin/login")}catch(e){console.error("Logout error:",e)}},h=[{name:"Dashboard",href:"/admin/dashboard",icon:"dashboard"},{name:"Announcements",href:"/admin/announcements",icon:"announcements"},{name:"Faculty",href:"/admin/faculty",icon:"faculty"},{name:"Gallery",href:"/admin/gallery",icon:"gallery"},{name:"Achievements",href:"/admin/achievements",icon:"trophy"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Users",href:"/admin/users",icon:"users",adminOnly:!0},{name:"Audit Logs",href:"/admin/audit",icon:"audit",adminOnly:!0},{name:"Backup",href:"/admin/backup",icon:"backup",adminOnly:!0},{name:"Settings",href:"/admin/settings",icon:"settings"},{name:"Achievements",href:"/admin/achievements",icon:"achievements"},{name:"Content",href:"/admin/content",icon:"content"},{name:"Settings",href:"/admin/settings",icon:"settings"}],m=e=>{let s={dashboard:(0,n.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"})]}),announcements:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"})}),faculty:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),gallery:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),achievements:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})}),content:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),settings:(0,n.jsxs)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})};return s[e]||s.dashboard};return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,n.jsxs)("div",{className:"fixed inset-0 flex z-40 md:hidden ".concat(r?"":"hidden"),children:[(0,n.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>o(!1)}),(0,n.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,n.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,n.jsx)("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>o(!1),children:(0,n.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})}),(0,n.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,n.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,n.jsx)("h1",{className:"text-lg font-semibold text-green-600",children:"Admin Panel"})}),(0,n.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:h.filter(e=>!e.adminOnly||"super_admin"===t.role).map(e=>(0,n.jsxs)(a(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-base font-medium rounded-md",children:[m(e.icon),(0,n.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})]})]}),(0,n.jsx)("div",{className:"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",children:(0,n.jsx)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:(0,n.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,n.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,n.jsx)("h1",{className:"text-xl font-bold text-green-600",children:"Admin Panel"})}),(0,n.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:h.filter(e=>!e.adminOnly||"super_admin"===t.role).map(e=>(0,n.jsxs)(a(),{href:e.href,className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:[m(e.icon),(0,n.jsx)("span",{className:"ml-3",children:e.name})]},e.name))})]})})}),(0,n.jsxs)("div",{className:"md:pl-64 flex flex-col flex-1",children:[(0,n.jsx)("div",{className:"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-100",children:(0,n.jsx)("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500",onClick:()=>o(!0),children:(0,n.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})}),(0,n.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between h-16",children:[(0,n.jsx)("div",{className:"flex items-center",children:(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Meritorious School Admin"})}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(a(),{href:"/",className:"text-gray-500 hover:text-gray-700 text-sm",target:"_blank",children:"View Website"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-700",children:t.full_name}),(0,n.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:t.role}),(0,n.jsx)("button",{onClick:d,className:"text-gray-500 hover:text-gray-700 text-sm",children:"Logout"})]})]})]})})}),(0,n.jsx)("main",{className:"flex-1",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:s})})]})]})}},9340:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});var n=t(5155),i=t(2115),r=t(1280);function a(){let[e,s]=(0,i.useState)({}),[t,a]=(0,i.useState)(!0),[l,o]=(0,i.useState)(!1),[c,d]=(0,i.useState)(""),[h,m]=(0,i.useState)(""),[x,u]=(0,i.useState)("mission"),g=[{key:"mission",label:"Mission Statement",field:"mission_statement"},{key:"vision",label:"Vision Statement",field:"vision_statement"},{key:"about",label:"About School",field:"about_school"},{key:"principal",label:"Principal Message",field:"principal_message"},{key:"history",label:"School History",field:"school_history"},{key:"values",label:"Core Values",field:"core_values"},{key:"philosophy",label:"Academic Philosophy",field:"academic_philosophy"}];(0,i.useEffect)(()=>{f()},[]);let f=async()=>{try{let e=await fetch("/api/admin/settings");if(e.ok){let t=await e.json(),n={};g.forEach(e=>{t[e.field]&&(n[e.field]=t[e.field].value)}),s(n)}}catch(e){console.error("Error fetching content:",e),d("Failed to load content")}finally{a(!1)}},p=async s=>{s.preventDefault(),o(!0),d(""),m("");try{let s={settings:{}};g.forEach(t=>{let n=e[t.field]||"";s.settings[t.field]={value:n,type:"text",description:t.label,is_public:!0}});let t=await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),n=await t.json();t.ok?(m("Content updated successfully!"),setTimeout(()=>m(""),3e3)):d(n.error||"Failed to update content")}catch(e){d("An error occurred. Please try again.")}finally{o(!1)}};if(t)return(0,n.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,n.jsx)("div",{className:"animate-pulse space-y-4",children:[void 0,void 0,void 0].map((e,s)=>(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,n.jsx)("div",{className:"h-32 bg-gray-200 rounded"})]},s))})});let v=g.find(e=>e.key===x);return(0,n.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,n.jsx)("div",{className:"border-b border-gray-200",children:(0,n.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:g.map(e=>(0,n.jsx)("button",{onClick:()=>u(e.key),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(x===e.key?"border-green-500 text-green-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e.label},e.key))})}),(0,n.jsxs)("form",{onSubmit:p,className:"p-6",children:[c&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-6",children:c}),h&&(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm mb-6",children:h}),v&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:v.label}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:{mission:"Define the school's purpose and fundamental reason for existence.",vision:"Describe the school's aspirational future and long-term goals.",about:"Provide a comprehensive overview of the school, its facilities, and programs.",principal:"Share a welcome message from the principal to students and parents.",history:"Tell the story of how the school was founded and its journey over the years.",values:"List the core values and principles that guide the school's culture.",philosophy:"Explain the school's approach to education and learning methodology."}[v.key]||""})]}),(0,n.jsx)("div",{children:(0,n.jsx)(r.A,{content:e[v.field]||"",onChange:e=>{var t;return t=v.field,void s(s=>({...s,[t]:e}))},placeholder:"Enter ".concat(v.label.toLowerCase(),"..."),className:"min-h-[400px]"})})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200 mt-8",children:[(0,n.jsx)("div",{className:"text-sm text-gray-500",children:"Changes are saved across all tabs when you click Save"}),(0,n.jsx)("button",{type:"submit",disabled:l,className:"px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:l?"Saving...":"Save All Content"})]})]})]})}}},e=>{e.O(0,[5004,277,6874,5072,8441,5964,7358],()=>e(e.s=2214)),_N_E=e.O()}]);