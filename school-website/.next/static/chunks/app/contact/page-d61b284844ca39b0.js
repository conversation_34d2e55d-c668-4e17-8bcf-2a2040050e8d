(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{3811:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,5118)),Promise.resolve().then(r.bind(r,4615))},5118:(e,s,r)=>{"use strict";r.d(s,{default:()=>a});var n=r(5155),l=r(2115);function a(){let[e,s]=(0,l.useState)({name:"",email:"",phone:"",subject:"",message:"",submission_type:"general"}),[r,a]=(0,l.useState)(!1),[t,o]=(0,l.useState)(!1),[i,d]=(0,l.useState)(""),c=async r=>{r.preventDefault(),a(!0),d("");try{let r=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),n=await r.json();r.ok?(o(!0),s({name:"",email:"",phone:"",subject:"",message:"",submission_type:"general"})):d(n.error||"Failed to send message. Please try again.")}catch(e){d("Network error. Please check your connection and try again.")}finally{a(!1)}},u=e=>{let{name:r,value:n}=e.target;s(e=>({...e,[r]:n}))};return t?(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,n.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Message Sent Successfully!"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"Thank you for contacting us. We have received your message and will get back to you within 24-48 hours."}),(0,n.jsx)("button",{onClick:()=>o(!1),className:"bg-green-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors",children:"Send Another Message"})]})}):(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,n.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Send us a Message"}),i&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-6",children:i}),(0,n.jsxs)("form",{onSubmit:c,className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,n.jsx)("input",{type:"text",id:"name",name:"name",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.name,onChange:u,placeholder:"Enter your full name"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,n.jsx)("input",{type:"email",id:"email",name:"email",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.email,onChange:u,placeholder:"Enter your email address"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,n.jsx)("input",{type:"tel",id:"phone",name:"phone",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.phone,onChange:u,placeholder:"Enter your phone number"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"submission_type",className:"block text-sm font-medium text-gray-700 mb-2",children:"Inquiry Type"}),(0,n.jsx)("select",{id:"submission_type",name:"submission_type",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.submission_type,onChange:u,children:[{value:"general",label:"General Inquiry"},{value:"admission",label:"Admission Inquiry"},{value:"complaint",label:"Complaint"},{value:"suggestion",label:"Suggestion"},{value:"other",label:"Other"}].map(e=>(0,n.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Subject *"}),(0,n.jsx)("input",{type:"text",id:"subject",name:"subject",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.subject,onChange:u,placeholder:"Enter the subject of your message"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),(0,n.jsx)("textarea",{id:"message",name:"message",required:!0,rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:e.message,onChange:u,placeholder:"Enter your message here..."})]}),(0,n.jsx)("div",{children:(0,n.jsx)("button",{type:"submit",disabled:r,className:"w-full bg-green-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:r?(0,n.jsxs)("span",{className:"flex items-center justify-center",children:[(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending Message..."]}):"Send Message"})}),(0,n.jsx)("p",{className:"text-sm text-gray-500 text-center",children:"We typically respond to inquiries within 24-48 hours during business days."})]})]})}}},e=>{e.O(0,[6874,4615,8441,5964,7358],()=>e(e.s=3811)),_N_E=e.O()}]);