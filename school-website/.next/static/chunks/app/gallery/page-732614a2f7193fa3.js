(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6235],{558:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>x});var t=a(5155),i=a(2115),r=a(4615),l=a(6874),n=a.n(l);function c(){return(0,t.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-bold mb-4",children:"Contact Information"}),(0,t.jsx)("p",{className:"text-gray-300 mb-2",children:"Senior Secondary Residential School"}),(0,t.jsx)("p",{className:"text-gray-300 mb-2",children:"for Meritorious Students"}),(0,t.jsx)("p",{className:"text-gray-300 mb-2",children:"Civil Lines, Ludhiana, Punjab"}),(0,t.jsx)("p",{className:"text-gray-300",children:"Government of Punjab"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-bold mb-4",children:"Quick Links"}),(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/about",className:"text-gray-300 hover:text-white transition-colors",children:"About Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/academics",className:"text-gray-300 hover:text-white transition-colors",children:"Academics"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/gallery",className:"text-gray-300 hover:text-white transition-colors",children:"Gallery"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/faculty",className:"text-gray-300 hover:text-white transition-colors",children:"Faculty"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/admissions",className:"text-gray-300 hover:text-white transition-colors",children:"Admissions"})}),(0,t.jsx)("li",{children:(0,t.jsx)(n(),{href:"/contact",className:"text-gray-300 hover:text-white transition-colors",children:"Contact"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-bold mb-4",children:"Eligibility"}),(0,t.jsxs)("ul",{className:"text-gray-300 space-y-2",children:[(0,t.jsx)("li",{children:"• Minimum 80% in Matriculation"}),(0,t.jsx)("li",{children:"• Punjab state domicile"}),(0,t.jsx)("li",{children:"• Economically backward family"}),(0,t.jsx)("li",{children:"• State-wide entrance test"})]})]})]}),(0,t.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:(0,t.jsx)("p",{className:"text-gray-400",children:"\xa9 2024 Senior Secondary Residential School for Meritorious Students. All rights reserved. Government of Punjab."})})]})})}var o=a(3473),d=a(6766);function x(){let[e,s]=(0,i.useState)([]),[a,l]=(0,i.useState)([]),[n,x]=(0,i.useState)("all"),[h,m]=(0,i.useState)(!0),[u,p]=(0,i.useState)(null);(0,i.useEffect)(()=>{!async function(){try{let[e,a]=await Promise.all([(0,o._6)(),(0,o.hD)()]);s(e),l(["all",...a])}catch(e){p("Failed to load gallery"),console.error("Error fetching gallery:",e)}finally{m(!1)}}()},[]);let g="all"===n?e:e.filter(e=>e.category===n);return h?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(r.default,{}),(0,t.jsx)("section",{className:"bg-green-600 text-white py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Gallery"}),(0,t.jsx)("p",{className:"text-xl max-w-3xl mx-auto",children:"Explore our school life through photos and memories"})]})})}),(0,t.jsx)("section",{className:"py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,t.jsx)("div",{className:"bg-gray-200 aspect-square rounded-lg animate-pulse"},e))})})})]}):u?(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(r.default,{}),(0,t.jsx)("section",{className:"bg-green-600 text-white py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Gallery"}),(0,t.jsx)("p",{className:"text-xl max-w-3xl mx-auto",children:"Explore our school life through photos and memories"})]})})}),(0,t.jsx)("section",{className:"py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 text-center",children:(0,t.jsx)("p",{className:"text-red-600",children:u})})})})]}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(r.default,{}),(0,t.jsx)("section",{className:"bg-green-600 text-white py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"Gallery"}),(0,t.jsx)("p",{className:"text-xl max-w-3xl mx-auto",children:"Explore our school life through photos capturing moments of learning, achievement, and joy in our vibrant educational community."})]})})}),(0,t.jsx)("section",{className:"py-8 bg-white border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:a.filter(e=>e).map(e=>(0,t.jsx)("button",{onClick:()=>x(e),className:"px-6 py-2 rounded-full font-medium transition-colors ".concat(n===e?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:e&&e.charAt(0).toUpperCase()+e.slice(1)},e))})})}),(0,t.jsx)("section",{className:"py-16",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:0===g.length?(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-8",children:[(0,t.jsx)("svg",{className:"w-16 h-16 text-blue-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"No Images Found"}),(0,t.jsx)("p",{className:"text-blue-600",children:"all"===n?"Gallery images will be added soon.":'No images found in the "'.concat(n,'" category.')})]})}):(0,t.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(e=>(0,t.jsxs)("div",{className:"group relative bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow",children:[(0,t.jsxs)("div",{className:"aspect-square relative",children:[(0,t.jsx)(d.default,{src:e.image_url||"/placeholder-image.jpg",alt:e.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300"})]}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),e.description&&(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:e.description}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)("span",{className:"inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:e.category.charAt(0).toUpperCase()+e.category.slice(1)})})]})]},e.id))})})}),(0,t.jsx)(c,{})]})}},2618:(e,s,a)=>{Promise.resolve().then(a.bind(a,558))},3473:(e,s,a)=>{"use strict";a.d(s,{G8:()=>o,Wq:()=>m,_6:()=>d,hD:()=>x,yl:()=>h});var t=a(5647);let i=()=>"https://bqzbuhdantryrwyvhnnt.supabase.co".includes("supabase.co"),r=()=>{if(!i())throw Error("Supabase not configured");return(0,t.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxemJ1aGRhbnRyeXJ3eXZobm50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDE1OTcsImV4cCI6MjA2ODQxNzU5N30.Eb5fqPvaSZPEVj1ATu-JO90-chl8LreyjkXdxzOd2PM")},l=[{id:"1",title:"Admission 2024-25 Open",content:"Applications are now open for admission to Class 11th for the academic year 2024-25. Eligible students can apply online through our official website.",priority:"high",status:"published",is_published:!0,publish_date:"2024-03-01T10:00:00Z",created_at:"2024-03-01T10:00:00Z",updated_at:"2024-03-01T10:00:00Z"},{id:"2",title:"Annual Sports Day",content:"Annual Sports Day will be held on March 15, 2024. All students are encouraged to participate in various sporting events.",priority:"medium",status:"published",is_published:!0,publish_date:"2024-02-20T09:00:00Z",created_at:"2024-02-20T09:00:00Z",updated_at:"2024-02-20T09:00:00Z"}],n=[{id:"1",title:"JEE Advanced Qualification",description:"Student qualified for JEE Advanced 2023 with All India Rank under 5000",student_name:"Arjun Sharma",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-06-15",award_by:"IIT Council",position:"AIR 4500",image_url:void 0,certificate_url:void 0,status:"published",is_published:!0,created_at:"2023-06-15T00:00:00Z",updated_at:"2023-06-15T00:00:00Z"},{id:"2",title:"NEET Top Scorer",description:"Achieved 650+ marks in NEET 2023, securing admission in government medical college",student_name:"Priya Kaur",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-05-20",award_by:"NTA",position:"650/720",image_url:void 0,certificate_url:void 0,status:"published",is_published:!0,created_at:"2023-05-20T00:00:00Z",updated_at:"2023-05-20T00:00:00Z"}],c=[{id:"1",name:"Dr. Rajesh Kumar",designation:"Principal",department:"Administration",qualification:"Ph.D. in Education, M.A. Education",experience_years:25,image_url:void 0,email:"<EMAIL>",phone:void 0,bio:"Dr. Rajesh Kumar brings over 25 years of experience in educational administration.",is_published:!0,order_index:1,created_at:"2024-01-01T00:00:00Z",updated_at:"2024-01-01T00:00:00Z"}];async function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;if(!i())return l.slice(0,e);try{let s=r(),{data:a,error:t}=await s.from("announcements").select("*").eq("is_published",!0).order("publish_date",{ascending:!1}).limit(e);if(t)return console.error("Error fetching announcements:",t),l.slice(0,e);return a||l.slice(0,e)}catch(s){return console.error("Supabase error:",s),l.slice(0,e)}}async function d(e){return[]}async function x(){return[]}async function h(){if(!i())return c;try{let e=r(),{data:s,error:a}=await e.from("faculty").select("*").eq("is_published",!0).order("order_index",{ascending:!0});if(a)return console.error("Error fetching faculty:",a),c;return s||c}catch(e){return console.error("Supabase error:",e),c}}async function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;if(!i())return n.slice(0,e);try{let s=r(),{data:a,error:t}=await s.from("achievements").select("*").eq("is_published",!0).order("achievement_date",{ascending:!1}).limit(e);if(t)return console.error("Error fetching achievements:",t),n.slice(0,e);return a||n.slice(0,e)}catch(s){return console.error("Supabase error:",s),n.slice(0,e)}}}},e=>{e.O(0,[6874,5647,6766,4615,8441,5964,7358],()=>e(e.s=2618)),_N_E=e.O()}]);