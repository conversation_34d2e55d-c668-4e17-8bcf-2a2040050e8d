(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2309],{1813:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.bind(t,7300)),Promise.resolve().then(t.bind(t,4615))},3473:(e,a,t)=>{"use strict";t.d(a,{G8:()=>d,Wq:()=>h,_6:()=>o,hD:()=>u,yl:()=>m});var s=t(5647);let i=()=>"https://bqzbuhdantryrwyvhnnt.supabase.co".includes("supabase.co"),r=()=>{if(!i())throw Error("Supabase not configured");return(0,s.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxemJ1aGRhbnRyeXJ3eXZobm50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDE1OTcsImV4cCI6MjA2ODQxNzU5N30.Eb5fqPvaSZPEVj1ATu-JO90-chl8LreyjkXdxzOd2PM")},n=[{id:"1",title:"Admission 2024-25 Open",content:"Applications are now open for admission to Class 11th for the academic year 2024-25. Eligible students can apply online through our official website.",priority:"high",status:"published",is_published:!0,publish_date:"2024-03-01T10:00:00Z",created_at:"2024-03-01T10:00:00Z",updated_at:"2024-03-01T10:00:00Z"},{id:"2",title:"Annual Sports Day",content:"Annual Sports Day will be held on March 15, 2024. All students are encouraged to participate in various sporting events.",priority:"medium",status:"published",is_published:!0,publish_date:"2024-02-20T09:00:00Z",created_at:"2024-02-20T09:00:00Z",updated_at:"2024-02-20T09:00:00Z"}],l=[{id:"1",title:"JEE Advanced Qualification",description:"Student qualified for JEE Advanced 2023 with All India Rank under 5000",student_name:"Arjun Sharma",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-06-15",award_by:"IIT Council",position:"AIR 4500",image_url:void 0,certificate_url:void 0,status:"published",is_published:!0,created_at:"2023-06-15T00:00:00Z",updated_at:"2023-06-15T00:00:00Z"},{id:"2",title:"NEET Top Scorer",description:"Achieved 650+ marks in NEET 2023, securing admission in government medical college",student_name:"Priya Kaur",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-05-20",award_by:"NTA",position:"650/720",image_url:void 0,certificate_url:void 0,status:"published",is_published:!0,created_at:"2023-05-20T00:00:00Z",updated_at:"2023-05-20T00:00:00Z"}],c=[{id:"1",name:"Dr. Rajesh Kumar",designation:"Principal",department:"Administration",qualification:"Ph.D. in Education, M.A. Education",experience_years:25,image_url:void 0,email:"<EMAIL>",phone:void 0,bio:"Dr. Rajesh Kumar brings over 25 years of experience in educational administration.",is_published:!0,order_index:1,created_at:"2024-01-01T00:00:00Z",updated_at:"2024-01-01T00:00:00Z"}];async function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;if(!i())return n.slice(0,e);try{let a=r(),{data:t,error:s}=await a.from("announcements").select("*").eq("is_published",!0).order("publish_date",{ascending:!1}).limit(e);if(s)return console.error("Error fetching announcements:",s),n.slice(0,e);return t||n.slice(0,e)}catch(a){return console.error("Supabase error:",a),n.slice(0,e)}}async function o(e){return[]}async function u(){return[]}async function m(){if(!i())return c;try{let e=r(),{data:a,error:t}=await e.from("faculty").select("*").eq("is_published",!0).order("order_index",{ascending:!0});if(t)return console.error("Error fetching faculty:",t),c;return a||c}catch(e){return console.error("Supabase error:",e),c}}async function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;if(!i())return l.slice(0,e);try{let a=r(),{data:t,error:s}=await a.from("achievements").select("*").eq("is_published",!0).order("achievement_date",{ascending:!1}).limit(e);if(s)return console.error("Error fetching achievements:",s),l.slice(0,e);return t||l.slice(0,e)}catch(a){return console.error("Supabase error:",a),l.slice(0,e)}}},7300:(e,a,t)=>{"use strict";t.d(a,{default:()=>l});var s=t(5155),i=t(2115),r=t(3473),n=t(6766);function l(){let[e,a]=(0,i.useState)([]),[t,l]=(0,i.useState)(!0),[c,d]=(0,i.useState)(null);return((0,i.useEffect)(()=>{!async function(){try{let e=await (0,r.yl)();a(e)}catch(e){d("Failed to load faculty information"),console.error("Error fetching faculty:",e)}finally{l(!1)}}()},[]),t)?(0,s.jsx)("section",{className:"py-16 bg-white",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Our Faculty"}),(0,s.jsx)("div",{className:"w-24 h-1 bg-green-600 mx-auto"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[1,2,3,4,5,6].map(e=>(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 animate-pulse",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4"}),(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3 mx-auto"})]},e))})]})}):c?(0,s.jsx)("section",{className:"py-16 bg-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Our Faculty"}),(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:(0,s.jsx)("p",{className:"text-red-600",children:c})})]})})}):0===e.length?(0,s.jsx)("section",{className:"py-16 bg-white",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Our Faculty"}),(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,s.jsx)("p",{className:"text-blue-600",children:"Faculty information will be available soon."})})]})})}):(0,s.jsx)("section",{className:"py-16 bg-white",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Our Faculty"}),(0,s.jsx)("div",{className:"w-24 h-1 bg-green-600 mx-auto mb-6"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Meet our dedicated team of experienced educators committed to nurturing academic excellence and character development."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 text-center hover:shadow-lg transition-shadow",children:[(0,s.jsx)("div",{className:"mb-4",children:e.image_url?(0,s.jsx)(n.default,{src:e.image_url,alt:e.name,width:96,height:96,className:"w-24 h-24 rounded-full mx-auto object-cover"}):(0,s.jsx)("div",{className:"w-24 h-24 bg-green-100 rounded-full mx-auto flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-12 h-12 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-1",children:e.name}),(0,s.jsx)("p",{className:"text-green-600 font-medium mb-2",children:e.designation}),e.department&&(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.department}),e.qualification&&(0,s.jsx)("p",{className:"text-gray-700 text-sm mb-3",children:e.qualification}),e.experience_years&&(0,s.jsxs)("p",{className:"text-gray-600 text-sm mb-3",children:[e.experience_years," years of experience"]}),e.bio&&(0,s.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-4",children:e.bio.length>100?"".concat(e.bio.substring(0,100),"..."):e.bio}),e.email&&(0,s.jsxs)("div",{className:"flex items-center justify-center text-sm text-green-600",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),(0,s.jsx)("a",{href:"mailto:".concat(e.email),className:"hover:underline",children:"Contact"})]})]},e.id))})]})})}}},e=>{e.O(0,[6874,5647,6766,4615,8441,5964,7358],()=>e(e.s=1813)),_N_E=e.O()}]);