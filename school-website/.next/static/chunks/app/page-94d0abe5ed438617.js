(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{3473:(e,s,t)=>{"use strict";t.d(s,{G8:()=>d,Wq:()=>h,_6:()=>o,hD:()=>m,yl:()=>u});var a=t(5647);let r=()=>"https://bqzbuhdantryrwyvhnnt.supabase.co".includes("supabase.co"),n=()=>{if(!r())throw Error("Supabase not configured");return(0,a.UU)("https://bqzbuhdantryrwyvhnnt.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxemJ1aGRhbnRyeXJ3eXZobm50Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDE1OTcsImV4cCI6MjA2ODQxNzU5N30.Eb5fqPvaSZPEVj1ATu-JO90-chl8LreyjkXdxzOd2PM")},i=[{id:"1",title:"Admission 2024-25 Open",content:"Applications are now open for admission to Class 11th for the academic year 2024-25. Eligible students can apply online through our official website.",priority:"high",status:"published",is_published:!0,publish_date:"2024-03-01T10:00:00Z",created_at:"2024-03-01T10:00:00Z",updated_at:"2024-03-01T10:00:00Z"},{id:"2",title:"Annual Sports Day",content:"Annual Sports Day will be held on March 15, 2024. All students are encouraged to participate in various sporting events.",priority:"medium",status:"published",is_published:!0,publish_date:"2024-02-20T09:00:00Z",created_at:"2024-02-20T09:00:00Z",updated_at:"2024-02-20T09:00:00Z"}],l=[{id:"1",title:"JEE Advanced Qualification",description:"Student qualified for JEE Advanced 2023 with All India Rank under 5000",student_name:"Arjun Sharma",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-06-15",award_by:"IIT Council",position:"AIR 4500",image_url:void 0,certificate_url:void 0,status:"published",is_published:!0,created_at:"2023-06-15T00:00:00Z",updated_at:"2023-06-15T00:00:00Z"},{id:"2",title:"NEET Top Scorer",description:"Achieved 650+ marks in NEET 2023, securing admission in government medical college",student_name:"Priya Kaur",student_class:"Class 12",achievement_type:"academic",achievement_date:"2023-05-20",award_by:"NTA",position:"650/720",image_url:void 0,certificate_url:void 0,status:"published",is_published:!0,created_at:"2023-05-20T00:00:00Z",updated_at:"2023-05-20T00:00:00Z"}],c=[{id:"1",name:"Dr. Rajesh Kumar",designation:"Principal",department:"Administration",qualification:"Ph.D. in Education, M.A. Education",experience_years:25,image_url:void 0,email:"<EMAIL>",phone:void 0,bio:"Dr. Rajesh Kumar brings over 25 years of experience in educational administration.",is_published:!0,order_index:1,created_at:"2024-01-01T00:00:00Z",updated_at:"2024-01-01T00:00:00Z"}];async function d(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;if(!r())return i.slice(0,e);try{let s=n(),{data:t,error:a}=await s.from("announcements").select("*").eq("is_published",!0).order("publish_date",{ascending:!1}).limit(e);if(a)return console.error("Error fetching announcements:",a),i.slice(0,e);return t||i.slice(0,e)}catch(s){return console.error("Supabase error:",s),i.slice(0,e)}}async function o(e){return[]}async function m(){return[]}async function u(){if(!r())return c;try{let e=n(),{data:s,error:t}=await e.from("faculty").select("*").eq("is_published",!0).order("order_index",{ascending:!0});if(t)return console.error("Error fetching faculty:",t),c;return s||c}catch(e){return console.error("Supabase error:",e),c}}async function h(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;if(!r())return l.slice(0,e);try{let s=n(),{data:t,error:a}=await s.from("achievements").select("*").eq("is_published",!0).order("achievement_date",{ascending:!1}).limit(e);if(a)return console.error("Error fetching achievements:",a),l.slice(0,e);return t||l.slice(0,e)}catch(s){return console.error("Supabase error:",s),l.slice(0,e)}}},4366:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.bind(t,4820)),Promise.resolve().then(t.bind(t,6884)),Promise.resolve().then(t.bind(t,4615))},4820:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});var a=t(5155),r=t(2115),n=t(3473);function i(){let[e,s]=(0,r.useState)([]),[t,i]=(0,r.useState)(!0),[l,c]=(0,r.useState)(null);if((0,r.useEffect)(()=>{!async function(){try{let e=await (0,n.Wq)(6);s(e)}catch(e){c("Failed to load achievements"),console.error("Error fetching achievements:",e)}finally{i(!1)}}()},[]),t)return(0,a.jsx)("section",{className:"py-16 bg-green-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Student Achievements"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-green-600 mx-auto"})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-md animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]},e))})]})});if(l)return(0,a.jsx)("section",{className:"py-16 bg-green-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Student Achievements"}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:(0,a.jsx)("p",{className:"text-red-600",children:l})})]})})});if(0===e.length)return(0,a.jsx)("section",{className:"py-16 bg-green-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Student Achievements"}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,a.jsx)("p",{className:"text-blue-600",children:"Achievement records will be updated soon."})})]})})});let d=e=>{switch(e.toLowerCase()){case"academic":return"bg-blue-100 text-blue-800";case"sports":return"bg-green-100 text-green-800";case"cultural":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsx)("section",{className:"py-16 bg-green-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Student Achievements"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-green-600 mx-auto mb-6"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"Celebrating the outstanding accomplishments of our students in academics, sports, and cultural activities."})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>{var s;return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(d(e.achievement_type)),children:(e=>{switch(e.toLowerCase()){case"academic":return(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"})});case"sports":return(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})});case"cultural":return(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 3v10a2 2 0 002 2h6a2 2 0 002-2V7M7 7h10M9 11h6M9 15h6"})});default:return(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})})}})(e.achievement_type)}),(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(d(e.achievement_type)),children:e.achievement_type.charAt(0).toUpperCase()+e.achievement_type.slice(1)})]}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4 leading-relaxed",children:e.description}),e.student_name&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-700 mb-2",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),e.student_name]}),e.achievement_date&&(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),(s=e.achievement_date)?new Date(s).toLocaleDateString("en-IN",{year:"numeric",month:"short"}):""]})]},e.id)})}),(0,a.jsx)("div",{className:"text-center mt-12",children:(0,a.jsx)("button",{className:"bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors",children:"View All Achievements"})})]})})}},6884:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});var a=t(5155),r=t(2115),n=t(3473);function i(){let[e,s]=(0,r.useState)([]),[t,i]=(0,r.useState)(!0),[l,c]=(0,r.useState)(null);return((0,r.useEffect)(()=>{!async function(){try{let e=await (0,n.G8)(3);s(e)}catch(e){c("Failed to load announcements"),console.error("Error fetching announcements:",e)}finally{i(!1)}}()},[]),t)?(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Latest Announcements"}),(0,a.jsx)("div",{className:"animate-pulse space-y-4",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]},e))})]})})}):l?(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Latest Announcements"}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:(0,a.jsx)("p",{className:"text-red-600",children:l})})]})})}):0===e.length?(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Latest Announcements"}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,a.jsx)("p",{className:"text-blue-600",children:"No announcements available at the moment."})})]})})}):(0,a.jsx)("section",{className:"py-16 bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Latest Announcements"}),(0,a.jsx)("div",{className:"w-24 h-1 bg-green-600 mx-auto"})]}),(0,a.jsx)("div",{className:"space-y-6",children:e.map(e=>(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 flex-1",children:e.title}),(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium border ".concat((e=>{switch(e){case"high":return"bg-red-100 text-red-800 border-red-200";case"medium":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"low":return"bg-green-100 text-green-800 border-green-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(e.priority)),children:e.priority.toUpperCase()})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4 leading-relaxed",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Published on ",new Date(e.publish_date).toLocaleDateString("en-IN",{year:"numeric",month:"long",day:"numeric"})]})]})},e.id))}),(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsx)("button",{className:"bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors",children:"View All Announcements"})})]})})}}},e=>{e.O(0,[6874,5647,4615,8441,5964,7358],()=>e(e.s=4366)),_N_E=e.O()}]);