(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8676],{1280:(e,i,r)=>{"use strict";r.d(i,{A:()=>g});var n=r(5155),t=r(5109),l=r(1652),s=r(1891),o=r(6761),a=r(4109),c=r(4589),d=r(6377),u=r(2115);function g(e){let{content:i,onChange:r,placeholder:g="Start writing...",className:m=""}=e,[h,x]=(0,u.useState)(!1),[p,b]=(0,u.useState)(""),f=(0,t.hG)({extensions:[l.A,s.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),o.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-green-600 underline hover:text-green-700"}}),a.<PERSON>.configure({types:["heading","paragraph"]}),c.A,d.xJ],content:i,onUpdate:e=>{let{editor:i}=e;r(i.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}}});if(!f)return null;let v=e=>{let{onClick:i,isActive:r=!1,disabled:t=!1,children:l,title:s}=e;return(0,n.jsx)("button",{type:"button",onClick:i,disabled:t,title:s,className:"p-2 rounded text-sm font-medium transition-colors ".concat(r?"bg-green-100 text-green-700 border border-green-300":"text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent"," ").concat(t?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:l})};return(0,n.jsxs)("div",{className:"border border-gray-300 rounded-lg overflow-hidden ".concat(m),children:[(0,n.jsxs)("div",{className:"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1",children:[(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleBold().run(),isActive:f.isActive("bold"),title:"Bold",children:(0,n.jsx)("strong",{children:"B"})}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleItalic().run(),isActive:f.isActive("italic"),title:"Italic",children:(0,n.jsx)("em",{children:"I"})}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleStrike().run(),isActive:f.isActive("strike"),title:"Strikethrough",children:(0,n.jsx)("s",{children:"S"})})]}),(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:1}).run(),isActive:f.isActive("heading",{level:1}),title:"Heading 1",children:"H1"}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:2}).run(),isActive:f.isActive("heading",{level:2}),title:"Heading 2",children:"H2"}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleHeading({level:3}).run(),isActive:f.isActive("heading",{level:3}),title:"Heading 3",children:"H3"})]}),(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleBulletList().run(),isActive:f.isActive("bulletList"),title:"Bullet List",children:"•"}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleOrderedList().run(),isActive:f.isActive("orderedList"),title:"Numbered List",children:"1."})]}),(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("left").run(),isActive:f.isActive({textAlign:"left"}),title:"Align Left",children:"⬅"}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("center").run(),isActive:f.isActive({textAlign:"center"}),title:"Align Center",children:"↔"}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().setTextAlign("right").run(),isActive:f.isActive({textAlign:"right"}),title:"Align Right",children:"➡"})]}),(0,n.jsxs)("div",{className:"flex gap-1 border-r border-gray-300 pr-2 mr-2",children:[(0,n.jsx)(v,{onClick:()=>{b(f.getAttributes("link").href||""),x(!0)},isActive:f.isActive("link"),title:"Add Link",children:"\uD83D\uDD17"}),(0,n.jsx)(v,{onClick:()=>{let e=window.prompt("Enter image URL:");e&&f.chain().focus().setImage({src:e}).run()},title:"Add Image",children:"\uD83D\uDDBC"})]}),(0,n.jsxs)("div",{className:"flex gap-1",children:[(0,n.jsx)(v,{onClick:()=>f.chain().focus().toggleBlockquote().run(),isActive:f.isActive("blockquote"),title:"Quote",children:'"'}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().setHorizontalRule().run(),title:"Horizontal Rule",children:"―"}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().undo().run(),disabled:!f.can().chain().focus().undo().run(),title:"Undo",children:"↶"}),(0,n.jsx)(v,{onClick:()=>f.chain().focus().redo().run(),disabled:!f.can().chain().focus().redo().run(),title:"Redo",children:"↷"})]})]}),(0,n.jsxs)("div",{className:"min-h-[200px] bg-white",children:[(0,n.jsx)(t.$Z,{editor:f,className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none"}),f.isEmpty&&(0,n.jsx)("div",{className:"absolute top-16 left-4 text-gray-400 pointer-events-none",children:g})]}),h&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4",children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Add Link"}),(0,n.jsx)("input",{type:"url",value:p,onChange:e=>b(e.target.value),placeholder:"Enter URL",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 mb-4",autoFocus:!0}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{type:"button",onClick:()=>x(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,n.jsx)("button",{type:"button",onClick:()=>{""===p?f.chain().focus().extendMarkRange("link").unsetLink().run():f.chain().focus().extendMarkRange("link").setLink({href:p}).run(),x(!1),b("")},className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700",children:"Add Link"})]})]})})]})}},4346:(e,i,r)=>{"use strict";r.d(i,{default:()=>c});var n=r(5155),t=r(2115),l=r(5695),s=r(6766),o=r(603),a=r(1280);function c(e){let{faculty:i,isEdit:r=!1}=e,c=(0,l.useRouter)(),[d,u]=(0,t.useState)(!1),[g,m]=(0,t.useState)(!1),[h,x]=(0,t.useState)(""),[p,b]=(0,t.useState)({name:(null==i?void 0:i.name)||"",designation:(null==i?void 0:i.designation)||"",department:(null==i?void 0:i.department)||"",qualification:(null==i?void 0:i.qualification)||"",experience:(null==i?void 0:i.experience)||"",specialization:(null==i?void 0:i.specialization)||"",email:(null==i?void 0:i.email)||"",phone:(null==i?void 0:i.phone)||"",image_url:(null==i?void 0:i.image_url)||"",bio:(null==i?void 0:i.bio)||"",is_published:(null==i?void 0:i.is_published)===void 0||i.is_published}),f=async e=>{e.preventDefault(),u(!0),x("");try{let e=r?"/api/admin/faculty/".concat(null==i?void 0:i.id):"/api/admin/faculty",n=await fetch(e,{method:r?"PATCH":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)});if(n.ok)c.push("/admin/faculty");else{let e=await n.json();x(e.error||"Failed to save faculty member")}}catch(e){x("An error occurred. Please try again.")}finally{u(!1)}};return(0,n.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,n.jsxs)("form",{onSubmit:f,className:"space-y-6 p-6",children:[h&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm",children:h}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Profile Image"}),p.image_url&&(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)(s.default,{src:p.image_url,alt:"Current profile",width:128,height:128,className:"w-32 h-32 rounded-full object-cover"})}),(0,n.jsx)(o.A,{bucket:"faculty",folder:"profiles",validationType:"image",multiple:!1,onUploadStart:()=>m(!0),onUploadComplete:e=>{m(!1);let i=e.find(e=>e.success&&e.url);i&&b({...p,image_url:i.url})},className:"mb-4"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,n.jsx)("input",{type:"text",id:"name",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.name,onChange:e=>b({...p,name:e.target.value}),placeholder:"Enter full name"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"designation",className:"block text-sm font-medium text-gray-700 mb-2",children:"Designation *"}),(0,n.jsx)("input",{type:"text",id:"designation",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.designation,onChange:e=>b({...p,designation:e.target.value}),placeholder:"e.g., Principal, Teacher, HOD"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"department",className:"block text-sm font-medium text-gray-700 mb-2",children:"Department"}),(0,n.jsxs)("select",{id:"department",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.department,onChange:e=>b({...p,department:e.target.value}),children:[(0,n.jsx)("option",{value:"",children:"Select Department"}),["Mathematics","Science","English","Hindi","Social Studies","Computer Science","Physical Education","Arts","Commerce","Administration"].map(e=>(0,n.jsx)("option",{value:e,children:e},e))]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"qualification",className:"block text-sm font-medium text-gray-700 mb-2",children:"Qualification"}),(0,n.jsx)("input",{type:"text",id:"qualification",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.qualification,onChange:e=>b({...p,qualification:e.target.value}),placeholder:"e.g., M.A., B.Ed., Ph.D."})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"experience",className:"block text-sm font-medium text-gray-700 mb-2",children:"Experience"}),(0,n.jsx)("input",{type:"text",id:"experience",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.experience,onChange:e=>b({...p,experience:e.target.value}),placeholder:"e.g., 10 years"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"specialization",className:"block text-sm font-medium text-gray-700 mb-2",children:"Specialization"}),(0,n.jsx)("input",{type:"text",id:"specialization",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.specialization,onChange:e=>b({...p,specialization:e.target.value}),placeholder:"Area of expertise"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,n.jsx)("input",{type:"email",id:"email",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.email,onChange:e=>b({...p,email:e.target.value}),placeholder:"<EMAIL>"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone"}),(0,n.jsx)("input",{type:"tel",id:"phone",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500",value:p.phone,onChange:e=>b({...p,phone:e.target.value}),placeholder:"+91 9876543210"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-2",children:"Biography"}),(0,n.jsx)(a.A,{content:p.bio||"",onChange:e=>b({...p,bio:e}),placeholder:"Brief biography and achievements...",className:"min-h-[200px]"})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"checkbox",id:"is_published",className:"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded",checked:p.is_published,onChange:e=>b({...p,is_published:e.target.checked})}),(0,n.jsx)("label",{htmlFor:"is_published",className:"ml-2 block text-sm text-gray-700",children:"Publish profile"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between pt-6 border-t border-gray-200",children:[(0,n.jsx)("button",{type:"button",onClick:()=>c.back(),className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Cancel"}),(0,n.jsxs)("button",{type:"submit",disabled:d||g,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50",children:[g?"Uploading...":d?"Saving...":r?"Update":"Create"," Faculty"]})]})]})})}},8676:(e,i,r)=>{Promise.resolve().then(r.bind(r,6071)),Promise.resolve().then(r.bind(r,4346))}}]);