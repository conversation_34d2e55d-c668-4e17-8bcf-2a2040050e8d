[{"/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/about/page.tsx": "1", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/academics/page.tsx": "2", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admissions/page.tsx": "3", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/contact/page.tsx": "4", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/faculty/page.tsx": "5", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/gallery/page.tsx": "6", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/layout.tsx": "7", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/page.tsx": "8", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Achievements.tsx": "9", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Announcements.tsx": "10", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Faculty.tsx": "11", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Header.tsx": "12", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/data.ts": "13", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/supabase.ts": "14", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/types/database.ts": "15", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Footer.tsx": "16", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/dashboard/page.tsx": "17", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/login/page.tsx": "18", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/auth/login/route.ts": "19", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/auth/logout/route.ts": "20", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AdminLayout.tsx": "21", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/auth.ts": "22", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/announcements/[id]/edit/page.tsx": "23", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/announcements/new/page.tsx": "24", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/announcements/page.tsx": "25", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/announcements/[id]/route.ts": "26", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/announcements/route.ts": "27", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AnnouncementForm.tsx": "28", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AnnouncementsList.tsx": "29", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/status.ts": "30", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/upload/route.ts": "31", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/FileUpload.tsx": "32", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/upload.ts": "33", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/gallery/page.tsx": "34", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/gallery/upload/page.tsx": "35", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/gallery/[id]/route.ts": "36", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/gallery/bulk/route.ts": "37", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/gallery/route.ts": "38", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/GalleryManager.tsx": "39", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/GalleryUpload.tsx": "40", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/faculty/[id]/edit/page.tsx": "41", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/faculty/new/page.tsx": "42", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/faculty/page.tsx": "43", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/faculty/[id]/route.ts": "44", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/faculty/route.ts": "45", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/FacultyForm.tsx": "46", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/FacultyList.tsx": "47", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/init-db/route.ts": "48", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/init-db.ts": "49", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/achievements/page.tsx": "50", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/achievements/[id]/route.ts": "51", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/achievements/route.ts": "52", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AchievementsList.tsx": "53", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/achievements/[id]/edit/page.tsx": "54", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/achievements/new/page.tsx": "55", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AchievementForm.tsx": "56", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/contacts/[id]/route.ts": "57", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/contacts/route.ts": "58", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/contact/route.ts": "59", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/ContactForm.tsx": "60", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/audit/page.tsx": "61", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/backup/page.tsx": "62", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/content/page.tsx": "63", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/settings/page.tsx": "64", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/users/page.tsx": "65", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/audit/route.ts": "66", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/backup/route.ts": "67", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/export/route.ts": "68", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/scheduled-publish/route.ts": "69", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/settings/route.ts": "70", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/users/route.ts": "71", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/search/route.ts": "72", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/settings/route.ts": "73", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/search/page.tsx": "74", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/SearchComponent.tsx": "75", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AdminUsersList.tsx": "76", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AuditLogsList.tsx": "77", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/BackupManager.tsx": "78", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/ContentManagementForm.tsx": "79", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/RichTextEditor.tsx": "80", "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/SchoolSettingsForm.tsx": "81"}, {"size": 8682, "mtime": 1752853928150, "results": "82", "hashOfConfig": "83"}, {"size": 12964, "mtime": 1752845968483, "results": "84", "hashOfConfig": "83"}, {"size": 11746, "mtime": 1752845974859, "results": "85", "hashOfConfig": "83"}, {"size": 6963, "mtime": 1752852677744, "results": "86", "hashOfConfig": "83"}, {"size": 8525, "mtime": 1752845987708, "results": "87", "hashOfConfig": "83"}, {"size": 7297, "mtime": 1752845993650, "results": "88", "hashOfConfig": "83"}, {"size": 1340, "mtime": 1752841239505, "results": "89", "hashOfConfig": "83"}, {"size": 6435, "mtime": 1752845855150, "results": "90", "hashOfConfig": "83"}, {"size": 8283, "mtime": 1752850163775, "results": "91", "hashOfConfig": "83"}, {"size": 5355, "mtime": 1752846013345, "results": "92", "hashOfConfig": "83"}, {"size": 6234, "mtime": 1752846006841, "results": "93", "hashOfConfig": "83"}, {"size": 5319, "mtime": 1752853546251, "results": "94", "hashOfConfig": "83"}, {"size": 7937, "mtime": 1752852316702, "results": "95", "hashOfConfig": "83"}, {"size": 1514, "mtime": 1752842296300, "results": "96", "hashOfConfig": "83"}, {"size": 11328, "mtime": 1752853618105, "results": "97", "hashOfConfig": "83"}, {"size": 2269, "mtime": 1752844946192, "results": "98", "hashOfConfig": "83"}, {"size": 8985, "mtime": 1752846330912, "results": "99", "hashOfConfig": "83"}, {"size": 9443, "mtime": 1752849822807, "results": "100", "hashOfConfig": "83"}, {"size": 2005, "mtime": 1752846403764, "results": "101", "hashOfConfig": "83"}, {"size": 1564, "mtime": 1752846415732, "results": "102", "hashOfConfig": "83"}, {"size": 10379, "mtime": 1752854333251, "results": "103", "hashOfConfig": "83"}, {"size": 5522, "mtime": 1752846503048, "results": "104", "hashOfConfig": "83"}, {"size": 1832, "mtime": 1752846844465, "results": "105", "hashOfConfig": "83"}, {"size": 1200, "mtime": 1752846714361, "results": "106", "hashOfConfig": "83"}, {"size": 1099, "mtime": 1752846637152, "results": "107", "hashOfConfig": "83"}, {"size": 4303, "mtime": 1752847213091, "results": "108", "hashOfConfig": "83"}, {"size": 3365, "mtime": 1752853403872, "results": "109", "hashOfConfig": "83"}, {"size": 11797, "mtime": 1752853324869, "results": "110", "hashOfConfig": "83"}, {"size": 7192, "mtime": 1752847467501, "results": "111", "hashOfConfig": "83"}, {"size": 4818, "mtime": 1752847356430, "results": "112", "hashOfConfig": "83"}, {"size": 2405, "mtime": 1752847746717, "results": "113", "hashOfConfig": "83"}, {"size": 8819, "mtime": 1752847727860, "results": "114", "hashOfConfig": "83"}, {"size": 8669, "mtime": 1752847871732, "results": "115", "hashOfConfig": "83"}, {"size": 1087, "mtime": 1752848022893, "results": "116", "hashOfConfig": "83"}, {"size": 1202, "mtime": 1752848152157, "results": "117", "hashOfConfig": "83"}, {"size": 4714, "mtime": 1752848119779, "results": "118", "hashOfConfig": "83"}, {"size": 4064, "mtime": 1752848140331, "results": "119", "hashOfConfig": "83"}, {"size": 2892, "mtime": 1752848095716, "results": "120", "hashOfConfig": "83"}, {"size": 13283, "mtime": 1752849376044, "results": "121", "hashOfConfig": "83"}, {"size": 9443, "mtime": 1752849401837, "results": "122", "hashOfConfig": "83"}, {"size": 1768, "mtime": 1752848406336, "results": "123", "hashOfConfig": "83"}, {"size": 1166, "mtime": 1752848392600, "results": "124", "hashOfConfig": "83"}, {"size": 1066, "mtime": 1752848259676, "results": "125", "hashOfConfig": "83"}, {"size": 4721, "mtime": 1752848338494, "results": "126", "hashOfConfig": "83"}, {"size": 3059, "mtime": 1752848315393, "results": "127", "hashOfConfig": "83"}, {"size": 11125, "mtime": 1752853245514, "results": "128", "hashOfConfig": "83"}, {"size": 9346, "mtime": 1752849336132, "results": "129", "hashOfConfig": "83"}, {"size": 1636, "mtime": 1752849846817, "results": "130", "hashOfConfig": "83"}, {"size": 9244, "mtime": 1752852958014, "results": "131", "hashOfConfig": "83"}, {"size": 1103, "mtime": 1752850041009, "results": "132", "hashOfConfig": "83"}, {"size": 4988, "mtime": 1752850129289, "results": "133", "hashOfConfig": "83"}, {"size": 3277, "mtime": 1752850105706, "results": "134", "hashOfConfig": "83"}, {"size": 11289, "mtime": 1752850085462, "results": "135", "hashOfConfig": "83"}, {"size": 1816, "mtime": 1752852143786, "results": "136", "hashOfConfig": "83"}, {"size": 1195, "mtime": 1752852128390, "results": "137", "hashOfConfig": "83"}, {"size": 14066, "mtime": 1752853200614, "results": "138", "hashOfConfig": "83"}, {"size": 5683, "mtime": 1752852530373, "results": "139", "hashOfConfig": "83"}, {"size": 4306, "mtime": 1752852714603, "results": "140", "hashOfConfig": "83"}, {"size": 3912, "mtime": 1752852755356, "results": "141", "hashOfConfig": "83"}, {"size": 8525, "mtime": 1752852582823, "results": "142", "hashOfConfig": "83"}, {"size": 888, "mtime": 1752854120766, "results": "143", "hashOfConfig": "83"}, {"size": 898, "mtime": 1752854230401, "results": "144", "hashOfConfig": "83"}, {"size": 802, "mtime": 1752853810033, "results": "145", "hashOfConfig": "83"}, {"size": 794, "mtime": 1752853667164, "results": "146", "hashOfConfig": "83"}, {"size": 1189, "mtime": 1752853969234, "results": "147", "hashOfConfig": "83"}, {"size": 2223, "mtime": 1752854180900, "results": "148", "hashOfConfig": "83"}, {"size": 4533, "mtime": 1752854321382, "results": "149", "hashOfConfig": "83"}, {"size": 5416, "mtime": 1752854374185, "results": "150", "hashOfConfig": "83"}, {"size": 4468, "mtime": 1752853353421, "results": "151", "hashOfConfig": "83"}, {"size": 4865, "mtime": 1752854416338, "results": "152", "hashOfConfig": "83"}, {"size": 4153, "mtime": 1752854021841, "results": "153", "hashOfConfig": "83"}, {"size": 6453, "mtime": 1752854751033, "results": "154", "hashOfConfig": "83"}, {"size": 1203, "mtime": 1752853658177, "results": "155", "hashOfConfig": "83"}, {"size": 4591, "mtime": 1752854793165, "results": "156", "hashOfConfig": "83"}, {"size": 9112, "mtime": 1752854501167, "results": "157", "hashOfConfig": "83"}, {"size": 6382, "mtime": 1752853998900, "results": "158", "hashOfConfig": "83"}, {"size": 8811, "mtime": 1752854514123, "results": "159", "hashOfConfig": "83"}, {"size": 8200, "mtime": 1752854542654, "results": "160", "hashOfConfig": "83"}, {"size": 7341, "mtime": 1752854578587, "results": "161", "hashOfConfig": "83"}, {"size": 9535, "mtime": 1752854667676, "results": "162", "hashOfConfig": "83"}, {"size": 12862, "mtime": 1752854633756, "results": "163", "hashOfConfig": "83"}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ws9emy", {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/about/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/academics/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admissions/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/contact/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/faculty/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/gallery/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/layout.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Achievements.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Announcements.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Faculty.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Header.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/data.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/supabase.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/types/database.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/Footer.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/dashboard/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/login/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/auth/login/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/auth/logout/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AdminLayout.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/auth.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/announcements/[id]/edit/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/announcements/new/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/announcements/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/announcements/[id]/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/announcements/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AnnouncementForm.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AnnouncementsList.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/status.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/upload/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/FileUpload.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/upload.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/gallery/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/gallery/upload/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/gallery/[id]/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/gallery/bulk/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/gallery/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/GalleryManager.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/GalleryUpload.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/faculty/[id]/edit/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/faculty/new/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/faculty/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/faculty/[id]/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/faculty/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/FacultyForm.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/FacultyList.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/init-db/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/lib/init-db.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/achievements/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/achievements/[id]/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/achievements/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AchievementsList.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/achievements/[id]/edit/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/achievements/new/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AchievementForm.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/contacts/[id]/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/contacts/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/contact/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/ContactForm.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/audit/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/backup/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/content/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/settings/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/admin/users/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/audit/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/backup/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/export/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/scheduled-publish/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/settings/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/admin/users/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/search/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/api/settings/route.ts", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/app/search/page.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/SearchComponent.tsx", ["407"], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AdminUsersList.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/AuditLogsList.tsx", ["408"], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/BackupManager.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/ContentManagementForm.tsx", ["409"], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/RichTextEditor.tsx", [], [], "/Applications/XAMPP/xamppfiles/htdocs/MeritoriousSchoolLudhiana/school-website/src/components/admin/SchoolSettingsForm.tsx", [], [], {"ruleId": "410", "severity": 1, "message": "411", "line": 70, "column": 6, "nodeType": "412", "endLine": 70, "endColumn": 27, "suggestions": "413"}, {"ruleId": "410", "severity": 1, "message": "414", "line": 49, "column": 6, "nodeType": "412", "endLine": 49, "endColumn": 20, "suggestions": "415"}, {"ruleId": "410", "severity": 1, "message": "416", "line": 36, "column": 6, "nodeType": "412", "endLine": 36, "endColumn": 8, "suggestions": "417"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'performSearch'. Either include it or remove the dependency array.", "ArrayExpression", ["418"], "React Hook useEffect has a missing dependency: 'fetchLogs'. Either include it or remove the dependency array.", ["419"], "React Hook useEffect has a missing dependency: 'fetchContent'. Either include it or remove the dependency array.", ["420"], {"desc": "421", "fix": "422"}, {"desc": "423", "fix": "424"}, {"desc": "425", "fix": "426"}, "Update the dependencies array to be: [performSearch, query, selectedType]", {"range": "427", "text": "428"}, "Update the dependencies array to be: [fetchLogs, filter, page]", {"range": "429", "text": "430"}, "Update the dependencies array to be: [fetchContent]", {"range": "431", "text": "432"}, [1934, 1955], "[performSearch, query, selectedType]", [1079, 1093], "[fetchLogs, filter, page]", [1296, 1298], "[fetchContent]"]