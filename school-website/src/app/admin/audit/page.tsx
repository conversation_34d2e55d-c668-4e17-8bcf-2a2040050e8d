import { redirect } from 'next/navigation';
import { getCurrentAdmin } from '@/lib/auth';
import AdminLayout from '@/components/admin/AdminLayout';
import AuditLogsList from '@/components/admin/AuditLogsList';

export default async function AdminAuditLogs() {
  const currentAdmin = await getCurrentAdmin();

  if (!currentAdmin) {
    redirect('/admin/login');
  }

  // Only super_admin can view audit logs
  if (currentAdmin.role !== 'super_admin') {
    redirect('/admin');
  }

  return (
    <AdminLayout currentAdmin={currentAdmin}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Audit Logs</h1>
          <p className="text-gray-600">Track all admin actions and system activities</p>
        </div>

        {/* Audit Logs List */}
        <AuditLogsList />
      </div>
    </AdminLayout>
  );
}
