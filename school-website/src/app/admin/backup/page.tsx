import { redirect } from 'next/navigation';
import { getCurrentAdmin } from '@/lib/auth';
import AdminLayout from '@/components/admin/AdminLayout';
import BackupManager from '@/components/admin/BackupManager';

export default async function AdminBackup() {
  const currentAdmin = await getCurrentAdmin();

  if (!currentAdmin) {
    redirect('/admin/login');
  }

  // Only super_admin can manage backups
  if (currentAdmin.role !== 'super_admin') {
    redirect('/admin');
  }

  return (
    <AdminLayout currentAdmin={currentAdmin}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Backup & Export</h1>
          <p className="text-gray-600">Backup content, export data, and manage data protection</p>
        </div>

        {/* Backup Manager */}
        <BackupManager />
      </div>
    </AdminLayout>
  );
}
