import { redirect } from 'next/navigation';
import { getCurrentAdmin } from '@/lib/auth';
import AdminLayout from '@/components/admin/AdminLayout';
import ContentManagementForm from '@/components/admin/ContentManagementForm';

export default async function AdminContent() {
  const currentAdmin = await getCurrentAdmin();

  if (!currentAdmin) {
    redirect('/admin/login');
  }

  return (
    <AdminLayout currentAdmin={currentAdmin}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600">Manage mission, vision, and other school content</p>
        </div>

        {/* Content Form */}
        <ContentManagementForm />
      </div>
    </AdminLayout>
  );
}
