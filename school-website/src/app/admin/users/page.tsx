import { redirect } from 'next/navigation';
import { getCurrentAdmin } from '@/lib/auth';
import AdminLayout from '@/components/admin/AdminLayout';
import AdminUsersList from '@/components/admin/AdminUsersList';

export default async function AdminUsers() {
  const currentAdmin = await getCurrentAdmin();

  if (!currentAdmin) {
    redirect('/admin/login');
  }

  // Only super_admin can manage users
  if (currentAdmin.role !== 'super_admin') {
    redirect('/admin');
  }

  return (
    <AdminLayout currentAdmin={currentAdmin}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Admin User Management</h1>
            <p className="text-gray-600">Manage admin users and their permissions</p>
          </div>
          <a
            href="/admin/users/new"
            className="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"
          >
            Add Admin User
          </a>
        </div>

        {/* Users List */}
        <AdminUsersList />
      </div>
    </AdminLayout>
  );
}
