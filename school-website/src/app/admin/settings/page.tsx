import { redirect } from 'next/navigation';
import { getCurrentAdmin } from '@/lib/auth';
import AdminLayout from '@/components/admin/AdminLayout';
import SchoolSettingsForm from '@/components/admin/SchoolSettingsForm';

export default async function AdminSettings() {
  const currentAdmin = await getCurrentAdmin();

  if (!currentAdmin) {
    redirect('/admin/login');
  }

  return (
    <AdminLayout currentAdmin={currentAdmin}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">School Settings</h1>
          <p className="text-gray-600">Manage school branding, logo, and general settings</p>
        </div>

        {/* Settings Form */}
        <SchoolSettingsForm />
      </div>
    </AdminLayout>
  );
}
