import { redirect } from 'next/navigation';
import { getCurrentAdmin } from '@/lib/auth';
import AdminLayout from '@/components/admin/AdminLayout';
import FacultyForm from '@/components/admin/FacultyForm';
import { getSupabaseAdminClient } from '@/lib/supabase';

interface EditFacultyProps {
  params: Promise<{ id: string }>;
}

export default async function EditFaculty({ params }: EditFacultyProps) {
  const { id } = await params;
  const currentAdmin = await getCurrentAdmin();

  if (!currentAdmin) {
    redirect('/admin/login');
  }

  // Fetch faculty data
  const supabase = getSupabaseAdminClient();
  const { data: faculty, error } = await supabase
    .from('faculty')
    .select('*')
    .eq('id', id)
    .single();

  if (error || !faculty) {
    redirect('/admin/faculty');
  }

  return (
    <AdminLayout currentAdmin={currentAdmin}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <a
            href="/admin/faculty"
            className="text-gray-500 hover:text-gray-700"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </a>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Faculty Member</h1>
            <p className="text-gray-600">Update faculty profile information</p>
          </div>
        </div>

        {/* Form */}
        <FacultyForm faculty={faculty} isEdit={true} />
      </div>
    </AdminLayout>
  );
}
