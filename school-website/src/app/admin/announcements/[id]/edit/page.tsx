import { redirect } from 'next/navigation';
import { getCurrentAdmin } from '@/lib/auth';
import AdminLayout from '@/components/admin/AdminLayout';
import AnnouncementForm from '@/components/admin/AnnouncementForm';
import { getSupabaseAdminClient } from '@/lib/supabase';

interface EditAnnouncementProps {
  params: Promise<{ id: string }>;
}

export default async function EditAnnouncement({ params }: EditAnnouncementProps) {
  const { id } = await params;
  const currentAdmin = await getCurrentAdmin();

  if (!currentAdmin) {
    redirect('/admin/login');
  }

  // Fetch announcement data
  const supabase = getSupabaseAdminClient();
  const { data: announcement, error } = await supabase
    .from('announcements')
    .select('*')
    .eq('id', id)
    .single();

  if (error || !announcement) {
    redirect('/admin/announcements');
  }

  return (
    <AdminLayout currentAdmin={currentAdmin}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <a
            href="/admin/announcements"
            className="text-gray-500 hover:text-gray-700"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </a>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Announcement</h1>
            <p className="text-gray-600">Update announcement details</p>
          </div>
        </div>

        {/* Form */}
        <AnnouncementForm announcement={announcement} isEdit={true} />
      </div>
    </AdminLayout>
  );
}
