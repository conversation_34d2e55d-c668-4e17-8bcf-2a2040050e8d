import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

interface SearchResult {
  id: string;
  type: string;
  title: string;
  content: string;
  subtitle?: string;
  student?: string;
  studentClass?: string;
  achievementType?: string;
  priority?: string;
  specialization?: string;
  date?: string;
  awardBy?: string;
  position?: string;
  url: string;
  relevance: number;
}

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET /api/search - Search across content
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const type = searchParams.get('type') || 'all'; // all, announcements, faculty, achievements
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    if (!query || query.trim().length < 2) {
      return NextResponse.json({
        results: [],
        total: 0,
        message: 'Search query must be at least 2 characters long'
      });
    }

    const searchTerm = query.trim().toLowerCase();
    const results: SearchResult[] = [];
    let totalResults = 0;

    // Search Announcements
    if (type === 'all' || type === 'announcements') {
      const { data: announcements, error: announcementError } = await supabase
        .from('announcements')
        .select('id, title, content, priority, publish_date, created_at')
        .eq('is_published', true)
        .or(`title.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%`)
        .order('publish_date', { ascending: false })
        .range(offset, offset + limit - 1);

      if (!announcementError && announcements) {
        announcements.forEach(announcement => {
          results.push({
            id: announcement.id,
            type: 'announcement',
            title: announcement.title,
            content: announcement.content.substring(0, 200) + '...',
            priority: announcement.priority,
            date: announcement.publish_date,
            url: `/announcements/${announcement.id}`,
            relevance: calculateRelevance(searchTerm, announcement.title, announcement.content)
          });
        });
        totalResults += announcements.length;
      }
    }

    // Search Faculty
    if (type === 'all' || type === 'faculty') {
      const { data: faculty, error: facultyError } = await supabase
        .from('faculty')
        .select('id, name, designation, department, qualification, specialization, bio')
        .eq('is_published', true)
        .or(`name.ilike.%${searchTerm}%,designation.ilike.%${searchTerm}%,department.ilike.%${searchTerm}%,specialization.ilike.%${searchTerm}%,bio.ilike.%${searchTerm}%`)
        .order('name', { ascending: true })
        .range(offset, offset + limit - 1);

      if (!facultyError && faculty) {
        faculty.forEach(member => {
          results.push({
            id: member.id,
            type: 'faculty',
            title: member.name,
            content: `${member.designation} - ${member.department}`,
            subtitle: member.qualification,
            specialization: member.specialization,
            url: `/faculty/${member.id}`,
            relevance: calculateRelevance(searchTerm, member.name, member.bio || '', member.designation, member.specialization || '')
          });
        });
        totalResults += faculty.length;
      }
    }

    // Search Achievements
    if (type === 'all' || type === 'achievements') {
      const { data: achievements, error: achievementError } = await supabase
        .from('achievements')
        .select('id, title, description, student_name, student_class, achievement_type, achievement_date, award_by, position')
        .eq('is_published', true)
        .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,student_name.ilike.%${searchTerm}%,award_by.ilike.%${searchTerm}%`)
        .order('achievement_date', { ascending: false })
        .range(offset, offset + limit - 1);

      if (!achievementError && achievements) {
        achievements.forEach(achievement => {
          results.push({
            id: achievement.id,
            type: 'achievement',
            title: achievement.title,
            content: achievement.description?.substring(0, 200) + '...' || '',
            student: achievement.student_name,
            studentClass: achievement.student_class,
            achievementType: achievement.achievement_type,
            date: achievement.achievement_date,
            awardBy: achievement.award_by,
            position: achievement.position,
            url: `/achievements/${achievement.id}`,
            relevance: calculateRelevance(searchTerm, achievement.title, achievement.description || '', achievement.student_name || '')
          });
        });
        totalResults += achievements.length;
      }
    }

    // Sort results by relevance
    results.sort((a, b) => b.relevance - a.relevance);

    // Apply pagination to combined results
    const paginatedResults = results.slice(offset, offset + limit);

    return NextResponse.json({
      results: paginatedResults,
      total: totalResults,
      query: searchTerm,
      type,
      pagination: {
        offset,
        limit,
        hasMore: totalResults > offset + limit
      }
    });

  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Calculate relevance score for search results
function calculateRelevance(searchTerm: string, ...fields: string[]): number {
  let score = 0;
  const term = searchTerm.toLowerCase();

  fields.forEach((field, index) => {
    if (!field) return;
    
    const fieldLower = field.toLowerCase();
    
    // Exact match in title gets highest score
    if (index === 0 && fieldLower.includes(term)) {
      score += 100;
    }
    
    // Partial matches get lower scores
    if (fieldLower.includes(term)) {
      score += 50 - (index * 10);
    }
    
    // Word boundary matches get bonus
    const words = term.split(' ');
    words.forEach(word => {
      if (word.length > 2 && fieldLower.includes(word)) {
        score += 25 - (index * 5);
      }
    });
  });

  return score;
}
