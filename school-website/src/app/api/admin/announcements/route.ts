import { NextRequest, NextResponse } from 'next/server';
import { getCurrentAdmin, logAdminAction } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET /api/admin/announcements - List all announcements
export async function GET(_request: NextRequest) {
  try {
    const currentAdmin = await getCurrentAdmin();
    if (!currentAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: announcements, error } = await supabase
      .from('announcements')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Failed to fetch announcements' }, { status: 500 });
    }

    return NextResponse.json(announcements);
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/admin/announcements - Create new announcement
export async function POST(request: NextRequest) {
  try {
    const currentAdmin = await getCurrentAdmin();
    if (!currentAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      title,
      content,
      priority,
      is_published,
      publish_date,
      is_scheduled,
      scheduled_publish_date
    } = body;

    if (!title || !content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      );
    }

    // Handle scheduling logic
    let finalPublishDate = publish_date || new Date().toISOString();
    let finalIsPublished = is_published || false;
    let finalStatus = 'draft';

    if (is_scheduled && scheduled_publish_date) {
      finalPublishDate = scheduled_publish_date;
      finalIsPublished = false;
      finalStatus = 'scheduled';
    } else if (is_published) {
      finalStatus = 'published';
    }

    const { data: announcement, error } = await supabase
      .from('announcements')
      .insert({
        title,
        content,
        priority: priority || 'medium',
        status: finalStatus,
        is_published: finalIsPublished,
        publish_date: finalPublishDate,
      })
      .select()
      .single();

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Failed to create announcement' }, { status: 500 });
    }

    // Log admin action
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    await logAdminAction(
      currentAdmin.id,
      'CREATE_ANNOUNCEMENT',
      'announcements',
      announcement.id,
      undefined,
      { title, content, priority, is_published },
      clientIP,
      userAgent
    );

    return NextResponse.json(announcement, { status: 201 });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
