import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';
import bcrypt from 'bcryptjs';

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(_request: NextRequest) {
  try {
    // Check if database is accessible
    const connectionCheck = await checkDatabaseConnection();
    if (!connectionCheck.connected) {
      return NextResponse.json({
        success: false,
        error: 'Database connection failed',
        details: connectionCheck.error
      }, { status: 500 });
    }

    // Initialize database
    const result = await initializeDatabase();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        adminUser: result.adminUser
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Init DB API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const connectionCheck = await checkDatabaseConnection();
    
    return NextResponse.json({
      connected: connectionCheck.connected,
      error: connectionCheck.error || null,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    return NextResponse.json({
      connected: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
