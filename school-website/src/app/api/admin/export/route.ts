import { NextRequest, NextResponse } from 'next/server';
import { getCurrentAdmin, logAdminAction } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET /api/admin/export - Export data
export async function GET(request: NextRequest) {
  try {
    const currentAdmin = await getCurrentAdmin();
    if (!currentAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only super_admin can export data
    if (currentAdmin.role !== 'super_admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all';

    let exportData: Record<string, unknown> = {};
    const timestamp = new Date().toISOString();

    try {
      switch (type) {
        case 'announcements':
          const { data: announcements } = await supabase
            .from('announcements')
            .select('*')
            .order('created_at', { ascending: false });
          exportData = { announcements, exported_at: timestamp, type };
          break;

        case 'faculty':
          const { data: faculty } = await supabase
            .from('faculty')
            .select('*')
            .order('created_at', { ascending: false });
          exportData = { faculty, exported_at: timestamp, type };
          break;

        case 'achievements':
          const { data: achievements } = await supabase
            .from('achievements')
            .select('*')
            .order('created_at', { ascending: false });
          exportData = { achievements, exported_at: timestamp, type };
          break;

        case 'gallery':
          const { data: gallery } = await supabase
            .from('gallery')
            .select('*')
            .order('created_at', { ascending: false });
          exportData = { gallery, exported_at: timestamp, type };
          break;

        case 'contacts':
          const { data: contacts } = await supabase
            .from('contact_submissions')
            .select('*')
            .order('created_at', { ascending: false });
          exportData = { contacts, exported_at: timestamp, type };
          break;

        case 'settings':
          const { data: settings } = await supabase
            .from('school_settings')
            .select('*')
            .order('setting_key', { ascending: true });
          exportData = { settings, exported_at: timestamp, type };
          break;

        case 'all':
        default:
          // Export all data
          const [
            announcementsResult,
            facultyResult,
            achievementsResult,
            galleryResult,
            contactsResult,
            settingsResult
          ] = await Promise.all([
            supabase.from('announcements').select('*').order('created_at', { ascending: false }),
            supabase.from('faculty').select('*').order('created_at', { ascending: false }),
            supabase.from('achievements').select('*').order('created_at', { ascending: false }),
            supabase.from('gallery').select('*').order('created_at', { ascending: false }),
            supabase.from('contact_submissions').select('*').order('created_at', { ascending: false }),
            supabase.from('school_settings').select('*').order('setting_key', { ascending: true })
          ]);

          exportData = {
            announcements: announcementsResult.data,
            faculty: facultyResult.data,
            achievements: achievementsResult.data,
            gallery: galleryResult.data,
            contacts: contactsResult.data,
            settings: settingsResult.data,
            exported_at: timestamp,
            type: 'complete_backup',
            version: '1.0',
            school: 'Meritorious School Ludhiana'
          };
          break;
      }

      // Log admin action
      const clientIP = request.headers.get('x-forwarded-for') || 
                      request.headers.get('x-real-ip') || 
                      'unknown';
      const userAgent = request.headers.get('user-agent') || 'unknown';
      
      await logAdminAction(
        currentAdmin.id,
        'EXPORT_DATA',
        'system',
        undefined,
        undefined,
        { export_type: type, timestamp },
        clientIP,
        userAgent
      );

      // Create filename
      const dateStr = new Date().toISOString().split('T')[0];
      const filename = `${type}-export-${dateStr}.json`;

      // Return JSON file
      const jsonString = JSON.stringify(exportData, null, 2);
      
      return new NextResponse(jsonString, {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': jsonString.length.toString(),
        },
      });

    } catch (dbError) {
      console.error('Database error during export:', dbError);
      return NextResponse.json({ error: 'Failed to export data from database' }, { status: 500 });
    }

  } catch (error) {
    console.error('Export API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
