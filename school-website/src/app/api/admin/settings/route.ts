import { NextRequest, NextResponse } from 'next/server';
import { getCurrentAdmin, logAdminAction } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET /api/admin/settings - Get all school settings
export async function GET(_request: NextRequest) {
  try {
    const currentAdmin = await getCurrentAdmin();
    if (!currentAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: settings, error } = await supabase
      .from('school_settings')
      .select('*')
      .order('setting_key', { ascending: true });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 });
    }

    // Convert to key-value object for easier use
    const settingsObject = settings?.reduce((acc, setting) => {
      acc[setting.setting_key] = {
        value: setting.setting_value,
        type: setting.setting_type,
        description: setting.description,
        is_public: setting.is_public,
        updated_at: setting.updated_at
      };
      return acc;
    }, {} as Record<string, unknown>) || {};

    return NextResponse.json(settingsObject);

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/admin/settings - Update school settings
export async function POST(request: NextRequest) {
  try {
    const currentAdmin = await getCurrentAdmin();
    if (!currentAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { settings } = body;

    if (!settings || typeof settings !== 'object') {
      return NextResponse.json(
        { error: 'Settings object is required' },
        { status: 400 }
      );
    }

    const updatedSettings = [];
    const errors = [];

    // Process each setting
    for (const [key, value] of Object.entries(settings)) {
      try {
        const settingData = value as Record<string, unknown>;
        
        const { data: existingSetting } = await supabase
          .from('school_settings')
          .select('*')
          .eq('setting_key', key)
          .single();

        if (existingSetting) {
          // Update existing setting
          const { data: updated, error: updateError } = await supabase
            .from('school_settings')
            .update({
              setting_value: settingData.value,
              setting_type: settingData.type || 'text',
              description: settingData.description,
              is_public: settingData.is_public !== undefined ? settingData.is_public : true,
              updated_at: new Date().toISOString()
            })
            .eq('setting_key', key)
            .select()
            .single();

          if (updateError) {
            errors.push({ key, error: updateError.message });
          } else {
            updatedSettings.push(updated);
          }
        } else {
          // Create new setting
          const { data: created, error: createError } = await supabase
            .from('school_settings')
            .insert({
              setting_key: key,
              setting_value: settingData.value,
              setting_type: settingData.type || 'text',
              description: settingData.description,
              is_public: settingData.is_public !== undefined ? settingData.is_public : true,
            })
            .select()
            .single();

          if (createError) {
            errors.push({ key, error: createError.message });
          } else {
            updatedSettings.push(created);
          }
        }
      } catch (_settingError) {
        errors.push({ key, error: 'Invalid setting data' });
      }
    }

    // Log admin action
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    await logAdminAction(
      currentAdmin.id,
      'UPDATE_SCHOOL_SETTINGS',
      'school_settings',
      undefined,
      undefined,
      { updated_count: updatedSettings.length, errors_count: errors.length },
      clientIP,
      userAgent
    );

    return NextResponse.json({
      success: true,
      updated: updatedSettings.length,
      errors: errors.length,
      settings: updatedSettings,
      errorDetails: errors
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
