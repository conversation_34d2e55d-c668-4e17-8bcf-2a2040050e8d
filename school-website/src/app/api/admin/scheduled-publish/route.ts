import { NextRequest, NextResponse } from 'next/server';
import { getCurrentAdmin, logAdminAction } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// POST /api/admin/scheduled-publish - Process scheduled publications
export async function POST(request: NextRequest) {
  try {
    const currentAdmin = await getCurrentAdmin();
    if (!currentAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const now = new Date().toISOString();
    
    // Find announcements that should be published now
    const { data: scheduledAnnouncements, error: fetchError } = await supabase
      .from('announcements')
      .select('*')
      .eq('is_published', false)
      .eq('status', 'scheduled')
      .lte('publish_date', now);

    if (fetchError) {
      console.error('Error fetching scheduled announcements:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch scheduled content' }, { status: 500 });
    }

    if (!scheduledAnnouncements || scheduledAnnouncements.length === 0) {
      return NextResponse.json({ 
        message: 'No content ready for publishing',
        published: 0 
      });
    }

    // Update announcements to published status
    const announcementIds = scheduledAnnouncements.map(a => a.id);
    
    const { data: updatedAnnouncements, error: updateError } = await supabase
      .from('announcements')
      .update({ 
        is_published: true,
        status: 'published',
        updated_at: now
      })
      .in('id', announcementIds)
      .select();

    if (updateError) {
      console.error('Error updating announcements:', updateError);
      return NextResponse.json({ error: 'Failed to publish scheduled content' }, { status: 500 });
    }

    // Log admin action for each published announcement
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    for (const announcement of scheduledAnnouncements) {
      await logAdminAction(
        currentAdmin.id,
        'AUTO_PUBLISH_ANNOUNCEMENT',
        'announcements',
        announcement.id,
        { is_published: false, status: 'scheduled' },
        { is_published: true, status: 'published' },
        clientIP,
        userAgent
      );
    }

    return NextResponse.json({
      message: `Successfully published ${updatedAnnouncements?.length || 0} scheduled announcements`,
      published: updatedAnnouncements?.length || 0,
      announcements: updatedAnnouncements
    });

  } catch (error) {
    console.error('Scheduled publish API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET /api/admin/scheduled-publish - Get scheduled content summary
export async function GET(_request: NextRequest) {
  try {
    const currentAdmin = await getCurrentAdmin();
    if (!currentAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const now = new Date().toISOString();
    
    // Get scheduled announcements
    const { data: scheduledAnnouncements, error } = await supabase
      .from('announcements')
      .select('id, title, publish_date, status')
      .eq('is_published', false)
      .eq('status', 'scheduled')
      .order('publish_date', { ascending: true });

    if (error) {
      console.error('Error fetching scheduled content:', error);
      return NextResponse.json({ error: 'Failed to fetch scheduled content' }, { status: 500 });
    }

    // Separate ready to publish vs future scheduled
    const readyToPublish = scheduledAnnouncements?.filter(a => a.publish_date <= now) || [];
    const futureScheduled = scheduledAnnouncements?.filter(a => a.publish_date > now) || [];

    return NextResponse.json({
      readyToPublish: readyToPublish.length,
      futureScheduled: futureScheduled.length,
      totalScheduled: scheduledAnnouncements?.length || 0,
      items: {
        readyToPublish,
        futureScheduled
      }
    });

  } catch (error) {
    console.error('Scheduled content API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
