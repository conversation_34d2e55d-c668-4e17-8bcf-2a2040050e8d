import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Metadata } from "next";

// Function to fetch school content
async function getSchoolContent() {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/settings`, {
      cache: 'no-store' // Always fetch fresh content
    });

    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.error('Error fetching school content:', error);
  }

  // Return default content if API fails
  return {
    mission_statement: 'To provide quality education and nurture young minds for a better tomorrow.',
    vision_statement: 'To be a leading educational institution that empowers students to achieve excellence.',
    about_school: 'Meritorious School Ludhiana is committed to providing holistic education that develops both academic excellence and character.',
    principal_message: 'Welcome to our school family. We are dedicated to nurturing each student\'s potential.',
    school_history: 'Founded with a vision to provide quality education, our school has been serving the community for many years.',
    core_values: 'Excellence, Integrity, Innovation, and Inclusivity are the pillars of our educational philosophy.'
  };
}

export const metadata: Metadata = {
  title: "About Us - Senior Secondary Residential School for Meritorious Students",
  description: "Learn about our mission, vision, and history. Established in 2014 by Government of Punjab to provide free quality education to meritorious students.",
};

export default async function AboutPage() {
  const content = await getSchoolContent();
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-green-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About Our School</h1>
            <p className="text-xl max-w-3xl mx-auto">
              Dedicated to nurturing excellence and empowering meritorious students 
              from economically disadvantaged backgrounds since 2014.
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12">
            <div className="bg-white p-8 rounded-lg shadow-md">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <div
                className="text-lg text-gray-600 leading-relaxed prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: content.mission_statement || 'To provide quality education and holistic development to meritorious students from economically disadvantaged backgrounds.' }}
              />
            </div>

            <div className="bg-white p-8 rounded-lg shadow-md">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Vision</h2>
              <div
                className="text-lg text-gray-600 leading-relaxed prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: content.vision_statement || 'To be a premier residential institution that nurtures academic excellence, character building, and social responsibility among students.' }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* History */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our History</h2>
            <div className="w-24 h-1 bg-green-600 mx-auto"></div>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="bg-gray-50 p-8 rounded-lg">
              <p className="text-lg text-gray-700 leading-relaxed mb-6">
                Established in 2014 by the Government of Punjab, our school was created with 
                the noble vision of providing free quality education to meritorious students 
                from economically backward families. Since our inception, we have been committed 
                to breaking the barriers of economic inequality in education.
              </p>
              
              <p className="text-lg text-gray-700 leading-relaxed mb-6">
                Located in the heart of Civil Lines, Ludhiana, our institution has grown from 
                a small initiative to a comprehensive residential school that serves students 
                from across Punjab. We have consistently maintained high academic standards 
                while providing complete support including accommodation, meals, and coaching 
                for competitive examinations.
              </p>
              
              <p className="text-lg text-gray-700 leading-relaxed">
                Over the years, our students have achieved remarkable success in various 
                competitive examinations including JEE, NEET, CMA, and CA, proving that 
                with the right support and guidance, economic background need not be a 
                barrier to academic excellence.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">What Makes Us Special</h2>
            <p className="text-lg text-gray-600">Our unique approach to education and student development</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Academic Excellence</h3>
              <p className="text-gray-600">Rigorous curriculum with focus on competitive exam preparation and holistic development.</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Experienced Faculty</h3>
              <p className="text-gray-600">Dedicated teachers with years of experience in their respective subjects and competitive exam coaching.</p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Complete Care</h3>
              <p className="text-gray-600">Residential facility with nutritious meals, healthcare, and 24/7 security for student welfare.</p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
