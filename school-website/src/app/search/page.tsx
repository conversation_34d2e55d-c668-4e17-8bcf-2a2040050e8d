import { Metadata } from "next";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import SearchComponent from "@/components/SearchComponent";

export const metadata: Metadata = {
  title: "Search - Meritorious School Ludhiana",
  description: "Search for announcements, faculty information, achievements, and more at Meritorious School Ludhiana.",
  keywords: "search, announcements, faculty, achievements, Meritorious School Ludhiana",
};

export default function SearchPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Search Our Content
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Find announcements, faculty information, student achievements, and more across our website.
            </p>
          </div>

          {/* Search Component */}
          <div className="max-w-4xl mx-auto">
            <SearchComponent 
              placeholder="Search announcements, faculty, achievements..."
              className="w-full"
              showFilters={true}
            />
          </div>

          {/* Search Tips */}
          <div className="max-w-4xl mx-auto mt-12">
            <div className="bg-white rounded-lg shadow-md p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Search Tips</h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">What you can search for:</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-center">
                      <span className="text-green-600 mr-2">📢</span>
                      <span><strong>Announcements:</strong> School news, events, and updates</span>
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-600 mr-2">👨‍🏫</span>
                      <span><strong>Faculty:</strong> Teacher profiles, qualifications, and specializations</span>
                    </li>
                    <li className="flex items-center">
                      <span className="text-green-600 mr-2">🏆</span>
                      <span><strong>Achievements:</strong> Student accomplishments and awards</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Search tips:</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li>• Use specific keywords for better results</li>
                    <li>• Try different spellings if you don&apos;t find what you&apos;re looking for</li>
                    <li>• Use the content type filters to narrow your search</li>
                    <li>• Search for student names to find their achievements</li>
                    <li>• Look for teacher names or subjects to find faculty information</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Popular Searches */}
          <div className="max-w-4xl mx-auto mt-8">
            <div className="bg-white rounded-lg shadow-md p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Popular Searches</h2>
              
              <div className="flex flex-wrap gap-3">
                {[
                  'JEE Advanced',
                  'NEET results',
                  'Sports achievements',
                  'Science Olympiad',
                  'Mathematics teacher',
                  'Physics faculty',
                  'Cultural events',
                  'Academic calendar',
                  'Admission process',
                  'School events'
                ].map((term) => (
                  <span
                    key={term}
                    className="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm cursor-pointer hover:bg-green-200 transition-colors"
                  >
                    {term}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
