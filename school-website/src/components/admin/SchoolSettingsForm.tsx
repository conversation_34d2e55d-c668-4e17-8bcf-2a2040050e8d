"use client";

import { useState, useEffect } from 'react';
import Image from 'next/image';
import FileUpload from './FileUpload';

interface SchoolSettings {
  school_logo?: string;
  school_name?: string;
  school_tagline?: string;
  school_address?: string;
  school_phone?: string;
  school_email?: string;
  school_website?: string;
  principal_name?: string;
  principal_message?: string;
  established_year?: string;
}

export default function SchoolSettingsForm() {
  const [settings, setSettings] = useState<SchoolSettings>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings');
      if (response.ok) {
        const data = await response.json();
        
        // Convert the settings object to our form structure
        const formSettings: SchoolSettings = {};
        Object.keys(data).forEach(key => {
          formSettings[key as keyof SchoolSettings] = data[key].value;
        });
        
        setSettings(formSettings);
      }
    } catch (_error) {
      console.error('Error fetching settings:', _error);
      setError('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // Convert form data to API format
      const settingsData = {
        settings: {
          school_logo: {
            value: settings.school_logo || '',
            type: 'image',
            description: 'School logo image',
            is_public: true
          },
          school_name: {
            value: settings.school_name || '',
            type: 'text',
            description: 'Official school name',
            is_public: true
          },
          school_tagline: {
            value: settings.school_tagline || '',
            type: 'text',
            description: 'School tagline or motto',
            is_public: true
          },
          school_address: {
            value: settings.school_address || '',
            type: 'text',
            description: 'School physical address',
            is_public: true
          },
          school_phone: {
            value: settings.school_phone || '',
            type: 'text',
            description: 'School contact phone number',
            is_public: true
          },
          school_email: {
            value: settings.school_email || '',
            type: 'text',
            description: 'School contact email',
            is_public: true
          },
          school_website: {
            value: settings.school_website || '',
            type: 'url',
            description: 'School website URL',
            is_public: true
          },
          principal_name: {
            value: settings.principal_name || '',
            type: 'text',
            description: 'Principal name',
            is_public: true
          },
          principal_message: {
            value: settings.principal_message || '',
            type: 'text',
            description: 'Principal welcome message',
            is_public: true
          },
          established_year: {
            value: settings.established_year || '',
            type: 'text',
            description: 'Year school was established',
            is_public: true
          }
        }
      };

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsData),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('Settings updated successfully!');
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(data.error || 'Failed to update settings');
      }
    } catch (_error) {
      setError('An error occurred. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (key: keyof SchoolSettings, value: string) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <form onSubmit={handleSubmit} className="space-y-6 p-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
            {success}
          </div>
        )}

        {/* School Logo */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            School Logo
          </label>
          
          {settings.school_logo && (
            <div className="mb-4">
              <Image
                src={settings.school_logo}
                alt="School Logo"
                width={120}
                height={120}
                className="w-30 h-30 object-contain rounded border"
              />
            </div>
          )}
          
          <FileUpload
            bucket="school"
            folder="branding"
            validationType="image"
            multiple={false}
            onUploadStart={() => setUploading(true)}
            onUploadComplete={(results) => {
              setUploading(false);
              const successfulUpload = results.find(result => result.success && result.url);
              if (successfulUpload) {
                handleInputChange('school_logo', successfulUpload.url!);
              }
            }}
            className="mb-4"
          />
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="school_name" className="block text-sm font-medium text-gray-700 mb-2">
              School Name
            </label>
            <input
              type="text"
              id="school_name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={settings.school_name || ''}
              onChange={(e) => handleInputChange('school_name', e.target.value)}
              placeholder="Enter school name"
            />
          </div>

          <div>
            <label htmlFor="school_tagline" className="block text-sm font-medium text-gray-700 mb-2">
              School Tagline
            </label>
            <input
              type="text"
              id="school_tagline"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={settings.school_tagline || ''}
              onChange={(e) => handleInputChange('school_tagline', e.target.value)}
              placeholder="Enter school tagline or motto"
            />
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <label htmlFor="school_address" className="block text-sm font-medium text-gray-700 mb-2">
            School Address
          </label>
          <textarea
            id="school_address"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            value={settings.school_address || ''}
            onChange={(e) => handleInputChange('school_address', e.target.value)}
            placeholder="Enter complete school address"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="school_phone" className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              id="school_phone"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={settings.school_phone || ''}
              onChange={(e) => handleInputChange('school_phone', e.target.value)}
              placeholder="Enter phone number"
            />
          </div>

          <div>
            <label htmlFor="school_email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="school_email"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={settings.school_email || ''}
              onChange={(e) => handleInputChange('school_email', e.target.value)}
              placeholder="Enter email address"
            />
          </div>

          <div>
            <label htmlFor="school_website" className="block text-sm font-medium text-gray-700 mb-2">
              Website URL
            </label>
            <input
              type="url"
              id="school_website"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={settings.school_website || ''}
              onChange={(e) => handleInputChange('school_website', e.target.value)}
              placeholder="Enter website URL"
            />
          </div>
        </div>

        {/* Principal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="principal_name" className="block text-sm font-medium text-gray-700 mb-2">
              Principal Name
            </label>
            <input
              type="text"
              id="principal_name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={settings.principal_name || ''}
              onChange={(e) => handleInputChange('principal_name', e.target.value)}
              placeholder="Enter principal name"
            />
          </div>

          <div>
            <label htmlFor="established_year" className="block text-sm font-medium text-gray-700 mb-2">
              Established Year
            </label>
            <input
              type="text"
              id="established_year"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={settings.established_year || ''}
              onChange={(e) => handleInputChange('established_year', e.target.value)}
              placeholder="Enter year established"
            />
          </div>
        </div>

        <div>
          <label htmlFor="principal_message" className="block text-sm font-medium text-gray-700 mb-2">
            Principal Message
          </label>
          <textarea
            id="principal_message"
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            value={settings.principal_message || ''}
            onChange={(e) => handleInputChange('principal_message', e.target.value)}
            placeholder="Enter principal welcome message"
          />
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end pt-6 border-t border-gray-200">
          <button
            type="submit"
            disabled={saving || uploading}
            className="px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
          >
            {uploading ? 'Uploading...' : saving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  );
}
