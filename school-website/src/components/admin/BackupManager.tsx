"use client";

import { useState } from 'react';

export default function BackupManager() {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const exportTypes = [
    { key: 'announcements', label: 'Announcements', description: 'Export all announcements with content and metadata' },
    { key: 'faculty', label: 'Faculty', description: 'Export faculty profiles and information' },
    { key: 'achievements', label: 'Achievements', description: 'Export student achievements and awards' },
    { key: 'gallery', label: 'Gallery', description: 'Export gallery images and metadata' },
    { key: 'contacts', label: 'Contact Submissions', description: 'Export contact form submissions' },
    { key: 'settings', label: 'School Settings', description: 'Export school configuration and settings' },
    { key: 'all', label: 'Complete Backup', description: 'Export all data in a comprehensive backup' },
  ];

  const handleExport = async (type: string) => {
    setLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await fetch(`/api/admin/export?type=${type}`, {
        method: 'GET',
      });

      if (response.ok) {
        // Get filename from response headers or create one
        const contentDisposition = response.headers.get('content-disposition');
        const filename = contentDisposition 
          ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
          : `${type}-export-${new Date().toISOString().split('T')[0]}.json`;

        // Create blob and download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        setMessage(`${type === 'all' ? 'Complete backup' : type} exported successfully!`);
      } else {
        const data = await response.json();
        setError(data.error || 'Export failed');
      }
    } catch (_error) {
      setError('Export failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBackupDatabase = async () => {
    setLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await fetch('/api/admin/backup', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Database backup created successfully!');
      } else {
        setError(data.error || 'Backup failed');
      }
    } catch (_error) {
      setError('Backup failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Status Messages */}
      {message && (
        <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm">
          {message}
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => handleExport('all')}
            disabled={loading}
            className="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            {loading ? 'Exporting...' : 'Export Complete Backup'}
          </button>

          <button
            onClick={handleBackupDatabase}
            disabled={loading}
            className="flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
            </svg>
            {loading ? 'Creating...' : 'Create Database Backup'}
          </button>
        </div>
      </div>

      {/* Individual Exports */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Individual Data Exports</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {exportTypes.filter(type => type.key !== 'all').map((type) => (
            <div key={type.key} className="border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">{type.label}</h3>
              <p className="text-sm text-gray-600 mb-4">{type.description}</p>
              
              <button
                onClick={() => handleExport(type.key)}
                disabled={loading}
                className="w-full flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Backup Information */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Backup Information</h2>
        
        <div className="prose prose-sm text-gray-600">
          <ul>
            <li><strong>Export Format:</strong> Data is exported in JSON format for easy import/restore</li>
            <li><strong>File Handling:</strong> Images and files are referenced by URL in exports</li>
            <li><strong>Security:</strong> Sensitive data like passwords are excluded from exports</li>
            <li><strong>Frequency:</strong> Regular backups are recommended weekly or before major changes</li>
            <li><strong>Storage:</strong> Store backups securely and in multiple locations</li>
          </ul>
        </div>

        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex">
            <svg className="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <h3 className="text-sm font-medium text-yellow-800">Important Note</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Backup and export operations are only available to Super Admin users. 
                Always verify backup integrity before relying on them for recovery.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
