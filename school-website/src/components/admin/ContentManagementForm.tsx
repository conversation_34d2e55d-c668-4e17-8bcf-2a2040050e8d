"use client";

import { useState, useEffect } from 'react';
import RichTextEditor from './RichTextEditor';

interface ContentData {
  mission_statement?: string;
  vision_statement?: string;
  about_school?: string;
  principal_message?: string;
  school_history?: string;
  core_values?: string;
  academic_philosophy?: string;
}

export default function ContentManagementForm() {
  const [content, setContent] = useState<ContentData>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('mission');

  const contentSections = [
    { key: 'mission', label: 'Mission Statement', field: 'mission_statement' },
    { key: 'vision', label: 'Vision Statement', field: 'vision_statement' },
    { key: 'about', label: 'About School', field: 'about_school' },
    { key: 'principal', label: 'Principal Message', field: 'principal_message' },
    { key: 'history', label: 'School History', field: 'school_history' },
    { key: 'values', label: 'Core Values', field: 'core_values' },
    { key: 'philosophy', label: 'Academic Philosophy', field: 'academic_philosophy' },
  ];

  useEffect(() => {
    fetchContent();
  }, []);

  const fetchContent = async () => {
    try {
      const response = await fetch('/api/admin/settings');
      if (response.ok) {
        const data = await response.json();
        
        // Convert the settings object to our form structure
        const formContent: ContentData = {};
        contentSections.forEach(section => {
          if (data[section.field]) {
            formContent[section.field as keyof ContentData] = data[section.field].value;
          }
        });
        
        setContent(formContent);
      }
    } catch (error) {
      console.error('Error fetching content:', error);
      setError('Failed to load content');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess('');

    try {
      // Convert form data to API format
      const settingsData = {
        settings: {}
      };

      contentSections.forEach(section => {
        const value = content[section.field as keyof ContentData] || '';
        (settingsData.settings as Record<string, unknown>)[section.field] = {
          value,
          type: 'text',
          description: section.label,
          is_public: true
        };
      });

      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settingsData),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('Content updated successfully!');
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(data.error || 'Failed to update content');
      }
    } catch (_error) {
      setError('An error occurred. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleContentChange = (field: keyof ContentData, value: string) => {
    setContent(prev => ({ ...prev, [field]: value }));
  };

  const getCurrentSection = () => {
    return contentSections.find(section => section.key === activeTab);
  };

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const currentSection = getCurrentSection();

  return (
    <div className="bg-white shadow rounded-lg">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {contentSections.map((section) => (
            <button
              key={section.key}
              onClick={() => setActiveTab(section.key)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === section.key
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {section.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content Form */}
      <form onSubmit={handleSubmit} className="p-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm mb-6">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm mb-6">
            {success}
          </div>
        )}

        {currentSection && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {currentSection.label}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {getFieldDescription(currentSection.key)}
              </p>
            </div>

            <div>
              <RichTextEditor
                content={content[currentSection.field as keyof ContentData] || ''}
                onChange={(value) => handleContentChange(currentSection.field as keyof ContentData, value)}
                placeholder={`Enter ${currentSection.label.toLowerCase()}...`}
                className="min-h-[400px]"
              />
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200 mt-8">
          <div className="text-sm text-gray-500">
            Changes are saved across all tabs when you click Save
          </div>
          
          <button
            type="submit"
            disabled={saving}
            className="px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
          >
            {saving ? 'Saving...' : 'Save All Content'}
          </button>
        </div>
      </form>
    </div>
  );
}

function getFieldDescription(key: string): string {
  const descriptions = {
    mission: 'Define the school\'s purpose and fundamental reason for existence.',
    vision: 'Describe the school\'s aspirational future and long-term goals.',
    about: 'Provide a comprehensive overview of the school, its facilities, and programs.',
    principal: 'Share a welcome message from the principal to students and parents.',
    history: 'Tell the story of how the school was founded and its journey over the years.',
    values: 'List the core values and principles that guide the school\'s culture.',
    philosophy: 'Explain the school\'s approach to education and learning methodology.'
  };
  
  return descriptions[key as keyof typeof descriptions] || '';
}
