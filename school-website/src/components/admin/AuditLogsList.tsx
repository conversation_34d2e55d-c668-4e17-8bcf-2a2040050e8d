"use client";

import { useState, useEffect } from 'react';

interface AuditLog {
  id: string;
  admin_id: string;
  action: string;
  table_name: string;
  record_id?: string;
  old_values?: Record<string, unknown>;
  new_values?: Record<string, unknown>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  admin_users?: {
    name: string;
    email: string;
  };
}

export default function AuditLogsList() {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const actionTypes = [
    'all',
    'CREATE_ANNOUNCEMENT',
    'UPDATE_ANNOUNCEMENT', 
    'DELETE_ANNOUNCEMENT',
    'CREATE_FACULTY',
    'UPDATE_FACULTY',
    'DELETE_FACULTY',
    'CREATE_ACHIEVEMENT',
    'UPDATE_ACHIEVEMENT',
    'DELETE_ACHIEVEMENT',
    'CREATE_ADMIN_USER',
    'UPDATE_ADMIN_USER',
    'DELETE_ADMIN_USER',
    'LOGIN',
    'LOGOUT'
  ];

  useEffect(() => {
    fetchLogs();
  }, [filter, page]);

  const fetchLogs = async () => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        ...(filter !== 'all' && { action: filter })
      });

      const response = await fetch(`/api/admin/audit?${params}`);
      if (response.ok) {
        const data = await response.json();
        setLogs(data.logs);
        setTotalPages(data.pagination.totalPages);
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionIcon = (action: string) => {
    if (action.includes('CREATE')) return '➕';
    if (action.includes('UPDATE')) return '✏️';
    if (action.includes('DELETE')) return '🗑️';
    if (action.includes('LOGIN')) return '🔐';
    if (action.includes('LOGOUT')) return '🚪';
    return '📝';
  };

  const getActionColor = (action: string) => {
    if (action.includes('CREATE')) return 'bg-green-100 text-green-800';
    if (action.includes('UPDATE')) return 'bg-blue-100 text-blue-800';
    if (action.includes('DELETE')) return 'bg-red-100 text-red-800';
    if (action.includes('LOGIN') || action.includes('LOGOUT')) return 'bg-purple-100 text-purple-800';
    return 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatAction = (action: string) => {
    return action.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex space-x-4">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      {/* Filters */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <label htmlFor="action-filter" className="block text-sm font-medium text-gray-700 mb-2">
              Filter by Action
            </label>
            <select
              id="action-filter"
              value={filter}
              onChange={(e) => {
                setFilter(e.target.value);
                setPage(1);
              }}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              {actionTypes.map(action => (
                <option key={action} value={action}>
                  {action === 'all' ? 'All Actions' : formatAction(action)}
                </option>
              ))}
            </select>
          </div>

          <div className="text-sm text-gray-500">
            Total logs: {logs.length > 0 ? `${(page - 1) * 20 + 1}-${Math.min(page * 20, logs.length)} of many` : '0'}
          </div>
        </div>
      </div>

      {/* Logs List */}
      <div className="divide-y divide-gray-200">
        {logs.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No audit logs found.
          </div>
        ) : (
          logs.map((log) => (
            <div key={log.id} className="p-4 hover:bg-gray-50">
              <div className="flex items-start space-x-3">
                <span className="text-lg">{getActionIcon(log.action)}</span>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>
                      {formatAction(log.action)}
                    </span>
                    <span className="text-sm text-gray-600">
                      on {log.table_name}
                    </span>
                    {log.record_id && (
                      <span className="text-xs text-gray-500 font-mono">
                        ID: {log.record_id.substring(0, 8)}...
                      </span>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-900 mb-1">
                    <span className="font-medium">
                      {log.admin_users?.name || 'Unknown Admin'}
                    </span>
                    <span className="text-gray-500 ml-2">
                      ({log.admin_users?.email || '<EMAIL>'})
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>{formatDate(log.created_at)}</span>
                    {log.ip_address && (
                      <span>IP: {log.ip_address}</span>
                    )}
                  </div>

                  {/* Show changes for update actions */}
                  {log.action.includes('UPDATE') && log.old_values && log.new_values && (
                    <details className="mt-2">
                      <summary className="text-xs text-blue-600 cursor-pointer hover:text-blue-800">
                        View Changes
                      </summary>
                      <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          <div>
                            <strong>Before:</strong>
                            <pre className="mt-1 text-xs text-gray-600 whitespace-pre-wrap">
                              {JSON.stringify(log.old_values, null, 2)}
                            </pre>
                          </div>
                          <div>
                            <strong>After:</strong>
                            <pre className="mt-1 text-xs text-gray-600 whitespace-pre-wrap">
                              {JSON.stringify(log.new_values, null, 2)}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </details>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="border-t border-gray-200 px-4 py-3 flex items-center justify-between">
          <button
            onClick={() => setPage(Math.max(1, page - 1))}
            disabled={page === 1}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <span className="text-sm text-gray-600">
            Page {page} of {totalPages}
          </span>
          
          <button
            onClick={() => setPage(Math.min(totalPages, page + 1))}
            disabled={page === totalPages}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
