"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import FileUpload from './FileUpload';
import RichTextEditor from './RichTextEditor';
import { ContentStatus, getStatusInfo, getInitialStatus } from '@/lib/status';

interface AchievementFormProps {
  achievement?: {
    id: string;
    title: string;
    description?: string;
    student_name?: string;
    student_class?: string;
    achievement_type: 'academic' | 'sports' | 'cultural' | 'competition' | 'scholarship' | 'other';
    achievement_date: string;
    award_by?: string;
    position?: string;
    image_url?: string;
    certificate_url?: string;
    status: ContentStatus;
    is_published: boolean;
  };
  isEdit?: boolean;
}

export default function AchievementForm({ achievement, isEdit = false }: AchievementFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  
  const [formData, setFormData] = useState({
    title: achievement?.title || '',
    description: achievement?.description || '',
    student_name: achievement?.student_name || '',
    student_class: achievement?.student_class || '',
    achievement_type: achievement?.achievement_type || 'academic' as 'academic' | 'sports' | 'cultural' | 'competition' | 'scholarship' | 'other',
    achievement_date: achievement?.achievement_date 
      ? new Date(achievement.achievement_date).toISOString().slice(0, 10)
      : new Date().toISOString().slice(0, 10),
    award_by: achievement?.award_by || '',
    position: achievement?.position || '',
    image_url: achievement?.image_url || '',
    certificate_url: achievement?.certificate_url || '',
    status: achievement?.status || getInitialStatus(),
    is_published: achievement?.is_published !== undefined ? achievement.is_published : true,
  });

  const achievementTypes = [
    { value: 'academic', label: 'Academic' },
    { value: 'sports', label: 'Sports' },
    { value: 'cultural', label: 'Cultural' },
    { value: 'competition', label: 'Competition' },
    { value: 'scholarship', label: 'Scholarship' },
    { value: 'other', label: 'Other' },
  ];

  const classes = [
    'Class 6', 'Class 7', 'Class 8', 'Class 9', 'Class 10',
    'Class 11', 'Class 12', 'Alumni'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const url = isEdit 
        ? `/api/admin/achievements/${achievement?.id}`
        : '/api/admin/achievements';
      
      const method = isEdit ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push('/admin/achievements');
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to save achievement');
      }
    } catch (_error) {
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <form onSubmit={handleSubmit} className="space-y-6 p-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        {/* Achievement Image Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Achievement Image (Optional)
          </label>
          
          {formData.image_url && (
            <div className="mb-4">
              <Image
                src={formData.image_url}
                alt="Achievement image"
                width={200}
                height={150}
                className="w-48 h-36 rounded object-cover"
              />
            </div>
          )}
          
          <FileUpload
            bucket="achievements"
            folder="images"
            validationType="image"
            multiple={false}
            onUploadStart={() => setUploading(true)}
            onUploadComplete={(results) => {
              setUploading(false);
              const successfulUpload = results.find(result => result.success && result.url);
              if (successfulUpload) {
                setFormData({ ...formData, image_url: successfulUpload.url! });
              }
            }}
            className="mb-4"
          />
        </div>

        {/* Certificate Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Certificate/Document (Optional)
          </label>
          
          {formData.certificate_url && (
            <div className="mb-4">
              <a
                href={formData.certificate_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                View Certificate
              </a>
            </div>
          )}
          
          <FileUpload
            bucket="achievements"
            folder="certificates"
            validationType="document"
            multiple={false}
            onUploadStart={() => setUploading(true)}
            onUploadComplete={(results) => {
              setUploading(false);
              const successfulUpload = results.find(result => result.success && result.url);
              if (successfulUpload) {
                setFormData({ ...formData, certificate_url: successfulUpload.url! });
              }
            }}
            className="mb-4"
          />
        </div>

        {/* Basic Information */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            Achievement Title *
          </label>
          <input
            type="text"
            id="title"
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            placeholder="Enter achievement title"
          />
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <RichTextEditor
            content={formData.description || ''}
            onChange={(description) => setFormData({ ...formData, description })}
            placeholder="Describe the achievement in detail..."
            className="min-h-[200px]"
          />
        </div>

        {/* Student Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="student_name" className="block text-sm font-medium text-gray-700 mb-2">
              Student Name
            </label>
            <input
              type="text"
              id="student_name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.student_name}
              onChange={(e) => setFormData({ ...formData, student_name: e.target.value })}
              placeholder="Enter student name"
            />
          </div>

          <div>
            <label htmlFor="student_class" className="block text-sm font-medium text-gray-700 mb-2">
              Class
            </label>
            <select
              id="student_class"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.student_class}
              onChange={(e) => setFormData({ ...formData, student_class: e.target.value })}
            >
              <option value="">Select Class</option>
              {classes.map(cls => (
                <option key={cls} value={cls}>{cls}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Achievement Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="achievement_type" className="block text-sm font-medium text-gray-700 mb-2">
              Achievement Type *
            </label>
            <select
              id="achievement_type"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.achievement_type}
              onChange={(e) => setFormData({ ...formData, achievement_type: e.target.value as 'academic' | 'sports' | 'cultural' | 'competition' | 'scholarship' | 'other' })}
            >
              {achievementTypes.map(type => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="achievement_date" className="block text-sm font-medium text-gray-700 mb-2">
              Achievement Date *
            </label>
            <input
              type="date"
              id="achievement_date"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.achievement_date}
              onChange={(e) => setFormData({ ...formData, achievement_date: e.target.value })}
            />
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              id="status"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as ContentStatus })}
            >
              <option value="new">New</option>
              <option value="draft">Draft</option>
              <option value="published">Published</option>
              <option value="archived">Archived</option>
            </select>
            <p className="mt-1 text-xs text-gray-500">
              {getStatusInfo(formData.status).description}
            </p>
          </div>
        </div>

        {/* Award Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="award_by" className="block text-sm font-medium text-gray-700 mb-2">
              Awarded By
            </label>
            <input
              type="text"
              id="award_by"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.award_by}
              onChange={(e) => setFormData({ ...formData, award_by: e.target.value })}
              placeholder="Organization or institution"
            />
          </div>

          <div>
            <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-2">
              Position/Rank
            </label>
            <input
              type="text"
              id="position"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.position}
              onChange={(e) => setFormData({ ...formData, position: e.target.value })}
              placeholder="e.g., 1st Place, AIR 100, Gold Medal"
            />
          </div>
        </div>

        {/* Published Status */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="is_published"
            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            checked={formData.is_published}
            onChange={(e) => setFormData({ ...formData, is_published: e.target.checked })}
          />
          <label htmlFor="is_published" className="ml-2 block text-sm text-gray-700">
            Publish achievement
          </label>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={loading || uploading}
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
          >
            {uploading ? 'Uploading...' : loading ? 'Saving...' : (isEdit ? 'Update' : 'Create')} Achievement
          </button>
        </div>
      </form>
    </div>
  );
}
