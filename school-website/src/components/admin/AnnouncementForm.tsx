"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ContentStatus, getStatusInfo, getInitialStatus } from '@/lib/status';
import FileUpload from './FileUpload';
import RichTextEditor from './RichTextEditor';

interface AnnouncementFormProps {
  announcement?: {
    id: string;
    title: string;
    content: string;
    priority: 'low' | 'medium' | 'high';
    status: ContentStatus;
    is_published: boolean;
    publish_date: string;
  };
  isEdit?: boolean;
}

export default function AnnouncementForm({ announcement, isEdit = false }: AnnouncementFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const [formData, setFormData] = useState({
    title: announcement?.title || '',
    content: announcement?.content || '',
    priority: announcement?.priority || 'medium' as 'low' | 'medium' | 'high',
    status: announcement?.status || getInitialStatus(),
    is_published: announcement?.is_published || false,
    publish_date: announcement?.publish_date
      ? new Date(announcement.publish_date).toISOString().slice(0, 16)
      : new Date().toISOString().slice(0, 16),
    is_scheduled: false,
    scheduled_publish_date: '',
  });

  const [attachments, setAttachments] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const url = isEdit 
        ? `/api/admin/announcements/${announcement?.id}`
        : '/api/admin/announcements';
      
      const method = isEdit ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          publish_date: new Date(formData.publish_date).toISOString(),
          attachments,
        }),
      });

      if (response.ok) {
        router.push('/admin/announcements');
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to save announcement');
      }
    } catch (_error) {
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDraft = async () => {
    const draftData = { ...formData, is_published: false };
    setFormData(draftData);
    
    // Trigger form submission with draft status
    const form = document.getElementById('announcement-form') as HTMLFormElement;
    if (form) {
      form.requestSubmit();
    }
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <form id="announcement-form" onSubmit={handleSubmit} className="space-y-6 p-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
            {error}
          </div>
        )}

        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            Title *
          </label>
          <input
            type="text"
            id="title"
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            placeholder="Enter announcement title"
          />
        </div>

        {/* Content */}
        <div>
          <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
            Content *
          </label>
          <RichTextEditor
            content={formData.content}
            onChange={(content) => setFormData({ ...formData, content })}
            placeholder="Enter announcement content..."
            className="min-h-[300px]"
          />
        </div>

        {/* Priority, Status and Publish Date */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <select
              id="priority"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.priority}
              onChange={(e) => setFormData({ ...formData, priority: e.target.value as 'low' | 'medium' | 'high' })}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              id="status"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as ContentStatus })}
            >
              <option value="new">New</option>
              <option value="draft">Draft</option>
              <option value="published">Published</option>
              <option value="archived">Archived</option>
            </select>
            <p className="mt-1 text-xs text-gray-500">
              {getStatusInfo(formData.status).description}
            </p>
          </div>

          <div>
            <label htmlFor="publish_date" className="block text-sm font-medium text-gray-700 mb-2">
              Publish Date
            </label>
            <input
              type="datetime-local"
              id="publish_date"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              value={formData.publish_date}
              onChange={(e) => setFormData({ ...formData, publish_date: e.target.value })}
            />
          </div>
        </div>

        {/* File Attachments */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Attachments (Optional)
          </label>
          <FileUpload
            bucket="announcements"
            folder="attachments"
            validationType="any"
            multiple={true}
            maxFiles={5}
            onUploadStart={() => setUploading(true)}
            onUploadComplete={(results) => {
              setUploading(false);
              const successfulUploads = results
                .filter(result => result.success && result.url)
                .map(result => result.url!);
              setAttachments(prev => [...prev, ...successfulUploads]);
            }}
            className="mb-4"
          />

          {/* Display current attachments */}
          {attachments.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-900">Attached Files:</h4>
              {attachments.map((url, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                  <span className="text-sm text-gray-700 truncate">
                    {url.split('/').pop()}
                  </span>
                  <button
                    type="button"
                    onClick={() => setAttachments(prev => prev.filter((_, i) => i !== index))}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Publishing Options */}
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_published"
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              checked={formData.is_published && !formData.is_scheduled}
              onChange={(e) => setFormData({
                ...formData,
                is_published: e.target.checked,
                is_scheduled: false
              })}
            />
            <label htmlFor="is_published" className="ml-2 block text-sm text-gray-700">
              Publish immediately
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_scheduled"
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              checked={formData.is_scheduled}
              onChange={(e) => setFormData({
                ...formData,
                is_scheduled: e.target.checked,
                is_published: false
              })}
            />
            <label htmlFor="is_scheduled" className="ml-2 block text-sm text-gray-700">
              Schedule for later
            </label>
          </div>

          {formData.is_scheduled && (
            <div className="ml-6">
              <label htmlFor="scheduled_publish_date" className="block text-sm font-medium text-gray-700 mb-2">
                Scheduled Publish Date & Time
              </label>
              <input
                type="datetime-local"
                id="scheduled_publish_date"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                value={formData.scheduled_publish_date}
                onChange={(e) => setFormData({ ...formData, scheduled_publish_date: e.target.value })}
                min={new Date().toISOString().slice(0, 16)}
              />
              <p className="mt-1 text-xs text-gray-500">
                The announcement will be automatically published at the scheduled time
              </p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            Cancel
          </button>
          
          <div className="flex space-x-3">
            {!isEdit && (
              <button
                type="button"
                onClick={handleSaveDraft}
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                Save as Draft
              </button>
            )}
            
            <button
              type="submit"
              disabled={loading || uploading}
              className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              {uploading ? 'Uploading...' : loading ? 'Saving...' : (isEdit ? 'Update' : 'Create')} Announcement
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
