"use client";

import Link from "next/link";
import { useState } from "react";
import SearchComponent from "./SearchComponent";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <h1 className="text-2xl font-bold text-green-900">
                Meritorious School Ludhiana
              </h1>
              <p className="text-sm text-gray-600">Government of Punjab</p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <Link href="/" className="text-green-900 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium">
                Home
              </Link>
              <Link href="/about" className="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium">
                About
              </Link>
              <Link href="/academics" className="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium">
                Academics
              </Link>
              <Link href="/gallery" className="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium">
                Gallery
              </Link>
              <Link href="/faculty" className="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium">
                Faculty
              </Link>
              <Link href="/admissions" className="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium">
                Admissions
              </Link>
              <Link href="/contact" className="text-gray-600 hover:text-green-700 px-3 py-2 rounded-md text-sm font-medium">
                Contact
              </Link>
            </div>
          </nav>

          {/* Search Component */}
          <div className="hidden md:block ml-6">
            <SearchComponent
              placeholder="Search..."
              className="w-64"
              showFilters={false}
            />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-600 hover:text-green-700 focus:outline-none focus:text-green-700"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 rounded-lg mb-4">
              <Link
                href="/"
                className="text-green-900 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                href="/about"
                className="text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                About
              </Link>
              <Link
                href="/academics"
                className="text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Academics
              </Link>
              <Link
                href="/gallery"
                className="text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Gallery
              </Link>
              <Link
                href="/faculty"
                className="text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Faculty
              </Link>
              <Link
                href="/admissions"
                className="text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Admissions
              </Link>
              <Link
                href="/contact"
                className="text-gray-600 hover:text-green-700 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsMenuOpen(false)}
              >
                Contact
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
