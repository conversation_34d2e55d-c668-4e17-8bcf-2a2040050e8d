// Simple data service that works with or without Supabase
// When Supabase is not configured, it returns mock data

import { Database } from '@/types/database';

type SchoolContent = Database['public']['Tables']['school_content']['Row'];
type Announcement = Database['public']['Tables']['announcements']['Row'];
type GalleryItem = Database['public']['Tables']['gallery']['Row'];
type Faculty = Database['public']['Tables']['faculty']['Row'];
type Achievement = Database['public']['Tables']['achievements']['Row'];

// Check if Supabase is configured
const isSupabaseConfigured = () => {
  return process.env.NEXT_PUBLIC_SUPABASE_URL &&
         process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY &&
         process.env.NEXT_PUBLIC_SUPABASE_URL !== 'your_supabase_project_url' &&
         process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY !== 'your_supabase_anon_key' &&
         process.env.NEXT_PUBLIC_SUPABASE_URL.includes('supabase.co');
};

// Import Supabase client
import { createClient } from '@supabase/supabase-js';

const getSupabaseClient = () => {
  if (!isSupabaseConfigured()) {
    throw new Error('Supabase not configured');
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
};

// Mock data for when Supabase is not configured
const mockAnnouncements: Announcement[] = [
  {
    id: '1',
    title: 'Admission 2024-25 Open',
    content: 'Applications are now open for admission to Class 11th for the academic year 2024-25. Eligible students can apply online through our official website.',
    priority: 'high',
    status: 'published',
    is_published: true,
    publish_date: '2024-03-01T10:00:00Z',
    created_at: '2024-03-01T10:00:00Z',
    updated_at: '2024-03-01T10:00:00Z'
  },
  {
    id: '2',
    title: 'Annual Sports Day',
    content: 'Annual Sports Day will be held on March 15, 2024. All students are encouraged to participate in various sporting events.',
    priority: 'medium',
    status: 'published',
    is_published: true,
    publish_date: '2024-02-20T09:00:00Z',
    created_at: '2024-02-20T09:00:00Z',
    updated_at: '2024-02-20T09:00:00Z'
  }
];

const mockAchievements: Achievement[] = [
  {
    id: '1',
    title: 'JEE Advanced Qualification',
    description: 'Student qualified for JEE Advanced 2023 with All India Rank under 5000',
    student_name: 'Arjun Sharma',
    student_class: 'Class 12',
    achievement_type: 'academic',
    achievement_date: '2023-06-15',
    award_by: 'IIT Council',
    position: 'AIR 4500',
    image_url: undefined,
    certificate_url: undefined,
    status: 'published',
    is_published: true,
    created_at: '2023-06-15T00:00:00Z',
    updated_at: '2023-06-15T00:00:00Z'
  },
  {
    id: '2',
    title: 'NEET Top Scorer',
    description: 'Achieved 650+ marks in NEET 2023, securing admission in government medical college',
    student_name: 'Priya Kaur',
    student_class: 'Class 12',
    achievement_type: 'academic',
    achievement_date: '2023-05-20',
    award_by: 'NTA',
    position: '650/720',
    image_url: undefined,
    certificate_url: undefined,
    status: 'published',
    is_published: true,
    created_at: '2023-05-20T00:00:00Z',
    updated_at: '2023-05-20T00:00:00Z'
  }
];

const mockFaculty: Faculty[] = [
  {
    id: '1',
    name: 'Dr. Rajesh Kumar',
    designation: 'Principal',
    department: 'Administration',
    qualification: 'Ph.D. in Education, M.A. Education',
    experience_years: 25,
    image_url: undefined,
    email: '<EMAIL>',
    phone: undefined,
    bio: 'Dr. Rajesh Kumar brings over 25 years of experience in educational administration.',
    is_published: true,
    order_index: 1,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];



// Simplified functions that return mock data for now
// These will be updated when Supabase is properly configured

export async function getLatestAnnouncements(limit: number = 5): Promise<Announcement[]> {
  if (!isSupabaseConfigured()) {
    return mockAnnouncements.slice(0, limit);
  }

  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('announcements')
      .select('*')
      .eq('is_published', true)
      .order('publish_date', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching announcements:', error);
      return mockAnnouncements.slice(0, limit);
    }

    return data || mockAnnouncements.slice(0, limit);
  } catch (err) {
    console.error('Supabase error:', err);
    return mockAnnouncements.slice(0, limit);
  }
}

export async function getAnnouncementsByPriority(priority: 'low' | 'medium' | 'high'): Promise<Announcement[]> {
  return mockAnnouncements.filter(a => a.priority === priority);
}

// Gallery Functions
export async function getGalleryByCategory(_category: string): Promise<GalleryItem[]> {
  return []; // Return empty for now
}

export async function getAllGalleryItems(_limit?: number): Promise<GalleryItem[]> {
  return []; // Return empty for now
}

export async function getGalleryCategories(): Promise<string[]> {
  return []; // Return empty for now
}

// Faculty Functions
export async function getFacultyByDepartment(department: string): Promise<Faculty[]> {
  return mockFaculty.filter(f => f.department === department);
}

export async function getAllFaculty(): Promise<Faculty[]> {
  if (!isSupabaseConfigured()) {
    return mockFaculty;
  }

  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('faculty')
      .select('*')
      .eq('is_published', true)
      .order('order_index', { ascending: true });

    if (error) {
      console.error('Error fetching faculty:', error);
      return mockFaculty;
    }

    return data || mockFaculty;
  } catch (err) {
    console.error('Supabase error:', err);
    return mockFaculty;
  }
}

// Achievements Functions
export async function getAchievementsByCategory(category: string): Promise<Achievement[]> {
  return mockAchievements.filter(a => a.achievement_type === category);
}

export async function getLatestAchievements(limit: number = 6): Promise<Achievement[]> {
  if (!isSupabaseConfigured()) {
    return mockAchievements.slice(0, limit);
  }

  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase
      .from('achievements')
      .select('*')
      .eq('is_published', true)
      .order('achievement_date', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching achievements:', error);
      return mockAchievements.slice(0, limit);
    }

    return data || mockAchievements.slice(0, limit);
  } catch (err) {
    console.error('Supabase error:', err);
    return mockAchievements.slice(0, limit);
  }
}

export async function getAchievementCategories(): Promise<string[]> {
  const categories = [...new Set(mockAchievements.map(item => item.achievement_type))];
  return categories;
}

// School Content Functions
export async function getSchoolContentBySection(_section: string): Promise<SchoolContent[]> {
  return []; // Return empty for now
}

export async function getAllSchoolContent(): Promise<SchoolContent[]> {
  return []; // Return empty for now
}

// Search Functions
export async function searchContent(query: string): Promise<{
  content: SchoolContent[];
  announcements: Announcement[];
  faculty: Faculty[];
}> {
  const lowerQuery = query.toLowerCase();

  return {
    content: [],
    announcements: mockAnnouncements.filter(a =>
      (a.title && a.title.toLowerCase().includes(lowerQuery)) ||
      (a.content && a.content.toLowerCase().includes(lowerQuery))
    ),
    faculty: mockFaculty.filter(f =>
      (f.name && f.name.toLowerCase().includes(lowerQuery)) ||
      (f.designation && f.designation.toLowerCase().includes(lowerQuery)) ||
      (f.department && f.department.toLowerCase().includes(lowerQuery))
    )
  };
}
