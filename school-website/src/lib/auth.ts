import bcrypt from 'bcryptjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/database';
import { getSupabaseAdminClient } from '@/lib/supabase';

const supabase = getSupabaseAdminClient();

export interface AdminUser {
  id: string;
  email: string;
  name: string;
  role: 'super_admin' | 'admin';
  is_active: boolean;
  last_login?: string;
  created_at: string;
  updated_at: string;
}

export interface AdminSession {
  id: string;
  admin_user_id: string;
  session_token: string;
  expires_at: string;
  created_at: string;
}

// Generate secure session token
export function generateSessionToken(): string {
  return crypto.randomUUID() + '-' + Date.now() + '-' + Math.random().toString(36);
}

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
}

// Verify password
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

// Authenticate admin user
export async function authenticateAdmin(email: string, password: string): Promise<AdminUser | null> {
  try {
    const { data: user, error } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .eq('is_active', true)
      .single();

    if (error || !user) {
      return null;
    }

    const isValidPassword = await verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      return null;
    }

    // Update last login
    await supabase
      .from('admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', user.id);

    // Return user without password hash
    const { password_hash: _password_hash, ...adminUser } = user;
    return adminUser as AdminUser;
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

// Create admin session
export async function createAdminSession(adminUserId: string): Promise<string | null> {
  try {
    const sessionToken = generateSessionToken();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

    const { error } = await supabase
      .from('admin_sessions')
      .insert({
        admin_user_id: adminUserId,
        session_token: sessionToken,
        expires_at: expiresAt.toISOString()
      });

    if (error) {
      console.error('Session creation error:', error);
      return null;
    }

    return sessionToken;
  } catch (error) {
    console.error('Session creation error:', error);
    return null;
  }
}

// Validate admin session
export async function validateAdminSession(sessionToken: string): Promise<AdminUser | null> {
  try {
    const { data: session, error } = await supabase
      .from('admin_sessions')
      .select(`
        *,
        admin_users (*)
      `)
      .eq('session_token', sessionToken)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error || !session || !session.admin_users) {
      return null;
    }

    const user = Array.isArray(session.admin_users) ? session.admin_users[0] : session.admin_users;
    const { password_hash: _password_hash, ...adminUser } = user;
    return adminUser as AdminUser;
  } catch (error) {
    console.error('Session validation error:', error);
    return null;
  }
}

// Get current admin user from cookies
export async function getCurrentAdmin(): Promise<AdminUser | null> {
  try {
    const cookieStore = await cookies();
    const sessionToken = cookieStore.get('admin_session')?.value;

    if (!sessionToken) {
      return null;
    }

    return await validateAdminSession(sessionToken);
  } catch (error) {
    console.error('Get current admin error:', error);
    return null;
  }
}

// Logout admin (delete session)
export async function logoutAdmin(sessionToken: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('admin_sessions')
      .delete()
      .eq('session_token', sessionToken);

    return !error;
  } catch (error) {
    console.error('Logout error:', error);
    return false;
  }
}

// Clean expired sessions
export async function cleanExpiredSessions(): Promise<void> {
  try {
    await supabase
      .from('admin_sessions')
      .delete()
      .lt('expires_at', new Date().toISOString());
  } catch (error) {
    console.error('Clean expired sessions error:', error);
  }
}

// Log admin action for audit
export async function logAdminAction(
  adminUserId: string,
  action: string,
  tableName?: string,
  recordId?: string,
  oldValues?: Record<string, unknown>,
  newValues?: Record<string, unknown>,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await supabase
      .from('audit_logs')
      .insert({
        admin_user_id: adminUserId,
        action,
        table_name: tableName,
        record_id: recordId,
        old_values: oldValues,
        new_values: newValues,
        ip_address: ipAddress,
        user_agent: userAgent
      });
  } catch (error) {
    console.error('Audit log error:', error);
  }
}

// Check admin permissions
export function hasPermission(user: AdminUser, requiredRole: 'super_admin' | 'admin' | 'editor'): boolean {
  const roleHierarchy = {
    'super_admin': 3,
    'admin': 2,
    'editor': 1
  };

  return roleHierarchy[user.role] >= roleHierarchy[requiredRole];
}
