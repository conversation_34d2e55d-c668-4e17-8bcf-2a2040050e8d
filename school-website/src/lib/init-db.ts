import { getSupabaseAdminClient } from '@/lib/supabase';
import bcrypt from 'bcryptjs';

export async function initializeDatabase() {
  try {
    console.log('Initializing database...');
    const supabase = getSupabaseAdminClient();

    // Check if admin user already exists
    const { data: existingAdmin } = await supabase
      .from('admin_users')
      .select('id')
      .eq('email', '<EMAIL>')
      .single();

    if (existingAdmin) {
      console.log('Admin user already exists');
      return { success: true, message: 'Database already initialized' };
    }

    // Create default admin user
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .insert({
        email: '<EMAIL>',
        password_hash: hashedPassword,
        name: 'System Administrator',
        role: 'super_admin',
        is_active: true,
      })
      .select()
      .single();

    if (adminError) {
      console.error('Error creating admin user:', adminError);
      return { success: false, error: adminError.message };
    }

    console.log('Admin user created:', adminUser.email);

    // Create sample announcements
    const sampleAnnouncements = [
      {
        title: 'Admission 2024-25 Open',
        content: 'Applications are now open for admission to Class 11th for the academic year 2024-25. Eligible students can apply online through our official website.',
        priority: 'high' as const,
        status: 'published' as const,
        is_published: true,
        publish_date: new Date().toISOString(),
      },
      {
        title: 'Annual Sports Day',
        content: 'Annual Sports Day will be held on March 15, 2024. All students are encouraged to participate in various sporting events.',
        priority: 'medium' as const,
        status: 'published' as const,
        is_published: true,
        publish_date: new Date().toISOString(),
      },
      {
        title: 'Parent-Teacher Meeting',
        content: 'Parent-Teacher meeting is scheduled for next week. Please check the notice board for detailed schedule.',
        priority: 'medium' as const,
        status: 'draft' as const,
        is_published: false,
        publish_date: new Date().toISOString(),
      }
    ];

    const { error: announcementError } = await supabase
      .from('announcements')
      .insert(sampleAnnouncements);

    if (announcementError) {
      console.error('Error creating sample announcements:', announcementError);
    } else {
      console.log('Sample announcements created');
    }

    // Create sample faculty
    const sampleFaculty = [
      {
        name: 'Dr. Rajesh Kumar',
        designation: 'Principal',
        department: 'Administration',
        qualification: 'Ph.D. in Education',
        experience: '25 years',
        specialization: 'Educational Administration',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        bio: 'Dr. Rajesh Kumar has been serving as Principal for over 10 years, dedicated to providing quality education to meritorious students.',
        is_published: true,
      },
      {
        name: 'Mrs. Priya Sharma',
        designation: 'Head of Mathematics Department',
        department: 'Mathematics',
        qualification: 'M.Sc. Mathematics, B.Ed.',
        experience: '15 years',
        specialization: 'Advanced Mathematics, JEE Preparation',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        bio: 'Mrs. Priya Sharma is an experienced mathematics teacher specializing in competitive exam preparation.',
        is_published: true,
      },
      {
        name: 'Dr. Amit Singh',
        designation: 'Physics Teacher',
        department: 'Science',
        qualification: 'Ph.D. in Physics',
        experience: '12 years',
        specialization: 'Quantum Physics, NEET Preparation',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        bio: 'Dr. Amit Singh brings extensive research experience to classroom teaching.',
        is_published: true,
      }
    ];

    const { error: facultyError } = await supabase
      .from('faculty')
      .insert(sampleFaculty);

    if (facultyError) {
      console.error('Error creating sample faculty:', facultyError);
    } else {
      console.log('Sample faculty created');
    }

    // Create sample gallery items
    const sampleGallery = [
      {
        title: 'School Campus',
        description: 'Beautiful view of our school campus',
        image_url: 'https://images.unsplash.com/photo-1580582932707-520aed937b7b?w=800',
        category: 'campus',
        status: 'published' as const,
        is_published: true,
      },
      {
        title: 'Science Laboratory',
        description: 'Well-equipped science laboratory for practical learning',
        image_url: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=800',
        category: 'academics',
        status: 'published' as const,
        is_published: true,
      },
      {
        title: 'Sports Day 2023',
        description: 'Annual sports day celebration',
        image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800',
        category: 'sports',
        status: 'published' as const,
        is_published: true,
      },
      {
        title: 'Cultural Event',
        description: 'Students performing in cultural program',
        image_url: 'https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=800',
        category: 'events',
        status: 'published' as const,
        is_published: true,
      }
    ];

    const { error: galleryError } = await supabase
      .from('gallery')
      .insert(sampleGallery);

    if (galleryError) {
      console.error('Error creating sample gallery:', galleryError);
    } else {
      console.log('Sample gallery created');
    }

    // Create sample achievements
    const sampleAchievements = [
      {
        title: 'JEE Advanced Qualification',
        description: 'Student qualified for JEE Advanced 2023 with All India Rank under 5000',
        student_name: 'Arjun Sharma',
        student_class: 'Class 12',
        achievement_type: 'academic' as const,
        achievement_date: '2023-06-15',
        award_by: 'IIT Council',
        position: 'AIR 4500',
        status: 'published' as const,
        is_published: true,
      },
      {
        title: 'State Level Basketball Championship',
        description: 'Won gold medal in state level basketball championship representing the school',
        student_name: 'Rajveer Singh',
        student_class: 'Class 11',
        achievement_type: 'sports' as const,
        achievement_date: '2023-04-10',
        award_by: 'Punjab Sports Council',
        position: '1st Place',
        status: 'published' as const,
        is_published: true,
      },
      {
        title: 'National Science Olympiad',
        description: 'Secured 2nd position in National Science Olympiad 2023',
        student_name: 'Ananya Gupta',
        student_class: 'Class 10',
        achievement_type: 'competition' as const,
        achievement_date: '2023-03-15',
        award_by: 'Science Olympiad Foundation',
        position: '2nd Place',
        status: 'published' as const,
        is_published: true,
      },
      {
        title: 'Inter-School Cultural Fest Winner',
        description: 'First prize in classical dance competition at inter-school cultural festival',
        student_name: 'Simran Kaur',
        student_class: 'Class 9',
        achievement_type: 'cultural' as const,
        achievement_date: '2023-02-28',
        award_by: 'Cultural Association',
        position: '1st Place',
        status: 'published' as const,
        is_published: true,
      }
    ];

    const { error: achievementError } = await supabase
      .from('achievements')
      .insert(sampleAchievements);

    if (achievementError) {
      console.error('Error creating sample achievements:', achievementError);
    } else {
      console.log('Sample achievements created');
    }

    return {
      success: true,
      message: 'Database initialized successfully',
      adminUser: {
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role
      }
    };

  } catch (error) {
    console.error('Database initialization error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Function to check database connection
export async function checkDatabaseConnection() {
  try {
    const supabase = getSupabaseAdminClient();
    const { data, error } = await supabase
      .from('admin_users')
      .select('count')
      .limit(1);

    if (error) {
      return { connected: false, error: error.message };
    }

    return { connected: true, data };
  } catch (error) {
    return { 
      connected: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
