export interface Database {
  public: {
    Tables: {
      admin_users: {
        Row: {
          id: string
          email: string
          password_hash: string
          full_name: string
          role: 'super_admin' | 'admin' | 'editor'
          is_active: boolean
          last_login?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          password_hash: string
          full_name: string
          role?: 'super_admin' | 'admin' | 'editor'
          is_active?: boolean
          last_login?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          password_hash?: string
          full_name?: string
          role?: 'super_admin' | 'admin' | 'editor'
          is_active?: boolean
          last_login?: string
          created_at?: string
          updated_at?: string
        }
      }
      admin_sessions: {
        Row: {
          id: string
          admin_user_id: string
          session_token: string
          expires_at: string
          created_at: string
        }
        Insert: {
          id?: string
          admin_user_id: string
          session_token: string
          expires_at: string
          created_at?: string
        }
        Update: {
          id?: string
          admin_user_id?: string
          session_token?: string
          expires_at?: string
          created_at?: string
        }
      }
      contact_submissions: {
        Row: {
          id: string
          name: string
          email: string
          phone?: string
          subject: string
          message: string
          submission_type: 'general' | 'admission' | 'complaint' | 'suggestion' | 'other'
          status: 'new' | 'read' | 'replied' | 'resolved' | 'archived'
          admin_notes?: string
          replied_at?: string
          replied_by?: string
          ip_address?: string
          user_agent?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email: string
          phone?: string
          subject: string
          message: string
          submission_type?: 'general' | 'admission' | 'complaint' | 'suggestion' | 'other'
          status?: 'new' | 'read' | 'replied' | 'resolved' | 'archived'
          admin_notes?: string
          replied_at?: string
          replied_by?: string
          ip_address?: string
          user_agent?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string
          phone?: string
          subject?: string
          message?: string
          submission_type?: 'general' | 'admission' | 'complaint' | 'suggestion' | 'other'
          status?: 'new' | 'read' | 'replied' | 'resolved' | 'archived'
          admin_notes?: string
          replied_at?: string
          replied_by?: string
          ip_address?: string
          user_agent?: string
          created_at?: string
          updated_at?: string
        }
      }
      audit_logs: {
        Row: {
          id: string
          admin_user_id?: string
          action: string
          table_name?: string
          record_id?: string
          old_values?: Record<string, unknown>
          new_values?: Record<string, unknown>
          ip_address?: string
          user_agent?: string
          created_at: string
        }
        Insert: {
          id?: string
          admin_user_id?: string
          action: string
          table_name?: string
          record_id?: string
          old_values?: Record<string, unknown>
          new_values?: Record<string, unknown>
          ip_address?: string
          user_agent?: string
          created_at?: string
        }
        Update: {
          id?: string
          admin_user_id?: string
          action?: string
          table_name?: string
          record_id?: string
          old_values?: Record<string, unknown>
          new_values?: Record<string, unknown>
          ip_address?: string
          user_agent?: string
          created_at?: string
        }
      }
      school_content: {
        Row: {
          id: string
          section: string
          title: string
          content: string
          image_url?: string
          order_index: number
          is_published: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          section: string
          title: string
          content: string
          image_url?: string
          order_index?: number
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          section?: string
          title?: string
          content?: string
          image_url?: string
          order_index?: number
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      announcements: {
        Row: {
          id: string
          title: string
          content: string
          priority: 'low' | 'medium' | 'high'
          status: 'new' | 'draft' | 'published' | 'archived'
          is_published: boolean
          publish_date: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          content: string
          priority?: 'low' | 'medium' | 'high'
          status?: 'new' | 'draft' | 'published' | 'archived'
          is_published?: boolean
          publish_date?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          content?: string
          priority?: 'low' | 'medium' | 'high'
          status?: 'new' | 'draft' | 'published' | 'archived'
          is_published?: boolean
          publish_date?: string
          created_at?: string
          updated_at?: string
        }
      }
      achievements: {
        Row: {
          id: string
          title: string
          description?: string
          student_name?: string
          student_class?: string
          achievement_type: 'academic' | 'sports' | 'cultural' | 'competition' | 'scholarship' | 'other'
          achievement_date: string
          award_by?: string
          position?: string
          image_url?: string
          certificate_url?: string
          status: 'new' | 'draft' | 'published' | 'archived'
          is_published: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string
          student_name?: string
          student_class?: string
          achievement_type: 'academic' | 'sports' | 'cultural' | 'competition' | 'scholarship' | 'other'
          achievement_date: string
          award_by?: string
          position?: string
          image_url?: string
          certificate_url?: string
          status?: 'new' | 'draft' | 'published' | 'archived'
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          student_name?: string
          student_class?: string
          achievement_type?: 'academic' | 'sports' | 'cultural' | 'competition' | 'scholarship' | 'other'
          achievement_date?: string
          award_by?: string
          position?: string
          image_url?: string
          certificate_url?: string
          status?: 'new' | 'draft' | 'published' | 'archived'
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      faculty: {
        Row: {
          id: string
          name: string
          designation: string
          department?: string
          qualification?: string
          experience_years?: number
          image_url?: string
          email?: string
          phone?: string
          bio?: string
          is_published: boolean
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          designation: string
          department?: string
          qualification?: string
          experience_years?: number
          image_url?: string
          email?: string
          phone?: string
          bio?: string
          is_published?: boolean
          order_index?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          designation?: string
          department?: string
          qualification?: string
          experience_years?: number
          image_url?: string
          email?: string
          phone?: string
          bio?: string
          is_published?: boolean
          order_index?: number
          created_at?: string
          updated_at?: string
        }
      }
      school_settings: {
        Row: {
          id: string
          setting_key: string
          setting_value?: string
          setting_type: 'text' | 'image' | 'url' | 'json' | 'boolean'
          description?: string
          is_public: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          setting_key: string
          setting_value?: string
          setting_type?: 'text' | 'image' | 'url' | 'json' | 'boolean'
          description?: string
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          setting_key?: string
          setting_value?: string
          setting_type?: 'text' | 'image' | 'url' | 'json' | 'boolean'
          description?: string
          is_public?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      gallery: {
        Row: {
          id: string
          title: string
          description?: string
          image_url: string
          category: string
          status: 'new' | 'draft' | 'published' | 'archived'
          is_published: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string
          image_url: string
          category: string
          status?: 'new' | 'draft' | 'published' | 'archived'
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          image_url?: string
          category?: string
          status?: 'new' | 'draft' | 'published' | 'archived'
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
